{"cells": [{"cell_type": "markdown", "id": "c20d8255", "metadata": {}, "source": ["これからやること  \n", "初期在庫更新式の反映  \n", "パラメータ値の実験  \n", "会社データの使用  "]}, {"cell_type": "code", "execution_count": null, "id": "619e1e89", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["品番数: 6, 期間: 20\n", "gen\tnevals\tavg        \tmin   \tmax    \n", "0  \t100   \t1.02082e+06\t601000\t1.4e+06\n", "1  \t85    \t913690     \t501000\t1.2e+06\n", "2  \t76    \t805580     \t501000\t1.1e+06\n", "3  \t80    \t742710     \t501000\t1.201e+06\n", "4  \t74    \t677790     \t500000\t1.001e+06\n", "5  \t73    \t607730     \t500000\t901000   \n", "6  \t77    \t539880     \t500000\t900000   \n", "7  \t72    \t529850     \t500000\t801000   \n", "8  \t85    \t536680     \t500000\t801000   \n", "9  \t85    \t526440     \t500000\t801000   \n", "10 \t80    \t518130     \t500000\t800000   \n", "11 \t77    \t502020     \t500000\t601000   \n", "12 \t68    \t500000     \t500000\t500000   \n", "13 \t72    \t501000     \t500000\t600000   \n", "14 \t82    \t500000     \t500000\t500000   \n", "15 \t78    \t500000     \t500000\t500000   \n", "16 \t69    \t500000     \t500000\t500000   \n", "17 \t83    \t500000     \t500000\t500000   \n", "18 \t60    \t502000     \t500000\t700000   \n", "19 \t83    \t500000     \t500000\t500000   \n", "20 \t76    \t503010     \t500000\t801000   \n", "21 \t74    \t503010     \t500000\t801000   \n", "22 \t69    \t503010     \t500000\t801000   \n", "23 \t72    \t500000     \t500000\t500000   \n", "24 \t77    \t506010     \t500000\t801000   \n", "25 \t70    \t506010     \t500000\t801000   \n", "26 \t79    \t501000     \t500000\t600000   \n", "27 \t85    \t500000     \t500000\t500000   \n", "28 \t74    \t500000     \t500000\t500000   \n", "29 \t80    \t504000     \t500000\t700000   \n", "30 \t69    \t504000     \t500000\t700000   \n", "31 \t78    \t500000     \t500000\t500000   \n", "32 \t76    \t500000     \t500000\t500000   \n", "33 \t83    \t505010     \t500000\t801000   \n", "34 \t84    \t507000     \t500000\t800000   \n", "35 \t80    \t502000     \t500000\t700000   \n", "36 \t71    \t510010     \t500000\t1e+06    \n", "37 \t68    \t502000     \t500000\t700000   \n", "38 \t73    \t500000     \t500000\t500000   \n", "39 \t78    \t500000     \t500000\t500000   \n", "40 \t79    \t505010     \t500000\t901000   \n", "41 \t77    \t503010     \t500000\t801000   \n", "42 \t69    \t505010     \t500000\t801000   \n", "43 \t82    \t501000     \t500000\t600000   \n", "44 \t68    \t502010     \t500000\t701000   \n", "45 \t80    \t500000     \t500000\t500000   \n", "46 \t78    \t502000     \t500000\t700000   \n", "47 \t73    \t500000     \t500000\t500000   \n", "48 \t76    \t501000     \t500000\t600000   \n", "49 \t73    \t500000     \t500000\t500000   \n", "50 \t74    \t506010     \t500000\t800000   \n", "\n", "=== 最適化結果 ===\n", "最良個体のペナルティ: 500000.00\n"]}], "source": ["\"\"\"\n", "遺伝的アルゴリズムによる生産スケジューリング最適化\n", "\"\"\"\n", "\n", "import random\n", "import numpy as np\n", "import pandas as pd\n", "from deap import base, creator, tools, algorithms\n", "import matplotlib.pyplot as plt\n", "import japanize_matplotlib\n", "\n", "# グローバル変数の初期化\n", "品番リスト = []\n", "出荷数リスト = []\n", "収容数リスト = []\n", "サイクルタイムリスト = []\n", "込め数リスト = []\n", "初期在庫量リスト = []\n", "品番数 = 0\n", "期間 = 20\n", "\n", "def read_csv(file_path):\n", "    \"\"\"CSVファイルを読み込む関数\"\"\"\n", "    global 品番リスト, 出荷数リスト, 収容数リスト, サイクルタイムリスト, 込め数リスト, 初期在庫量リスト\n", "    \n", "    try:\n", "        df = pd.read_csv(file_path, encoding='utf-8-sig')        \n", "        品番リスト = df['part_number'].tolist()\n", "        出荷数リスト = df['shipment'].tolist()\n", "        収容数リスト = df['capacity'].tolist()\n", "        サイクルタイムリスト = (df['cycle_time'] / 60).tolist()  # 分に換算\n", "        込め数リスト = df['cabity'].tolist()\n", "        初期在庫量リスト = df['initial_inventory'].tolist()\n", "        \n", "        return 品番リスト, 出荷数リスト, 収容数リスト, サイクルタイムリスト, 込め数リスト, 初期在庫量リスト\n", "    \n", "    except Exception as e:\n", "        print(f\"CSVファイル読み込みエラー: {e}\")\n", "        print(\"ファイルパスとエンコーディングを確認してください\")\n", "        return None, None, None, None, None, None\n", "\n", "def evaluate(ind):\n", "    \"\"\"評価関数\"\"\"\n", "    global 品番数, 期間\n", "    \n", "    # 各期間の結果を記録するリスト\n", "    品番在庫量 = [[] for _ in range(品番数)]\n", "    品番生産数 = [[] for _ in range(品番数)]\n", "    品番生産時間 = [[] for _ in range(品番数)]\n", "    \n", "    # ペナルティの累積値\n", "    total_setup_penalty = 0\n", "    total_overtime_penalty = 0\n", "    total_shortage_penalty = 0\n", "    \n", "    # 統計値の累積\n", "    total_production_time = 0\n", "    total_setup_count = 0\n", "    total_shortage_count = 0\n", "    \n", "    # 在庫の初期化\n", "    inventory = 初期在庫量リスト[:]\n", "    \n", "    for t in range(期間):\n", "        daily_time = 0\n", "        daily_setup = 0\n", "        daily_shortage = 0\n", "        \n", "        for i in range(品番数):\n", "            idx = t * 品番数 + i\n", "            production = ind[idx]\n", "            \n", "            # 在庫が十分にある場合は生産しない\n", "            if inventory[i] >= 出荷数リスト[i]:\n", "                production = 0\n", "            \n", "            # 段替え時間の計算\n", "            if production > 0:\n", "                setup_time = 1  # 段替え発生\n", "                daily_setup += 1\n", "            else:\n", "                setup_time = 0\n", "            \n", "            # 生産時間の計算\n", "            if サイクルタイムリスト[i] > 0 and 込め数リスト[i] > 0:\n", "                production_time = production / (サイクルタイムリスト[i] * 込め数リスト[i])\n", "            else:\n", "                production_time = 0\n", "            \n", "            daily_time += production_time + setup_time * 30\n", "            \n", "            # 在庫更新\n", "            inventory[i] += production - 出荷数リスト[i]\n", "            \n", "            # 在庫不足のペナルティ\n", "            if inventory[i] < 0:\n", "                daily_shortage += 1\n", "                total_shortage_penalty += 1000\n", "            \n", "            # 結果を記録\n", "            品番在庫量[i].append(inventory[i])\n", "            品番生産数[i].append(production)\n", "            品番生産時間[i].append(production_time)\n", "        \n", "        # 時間制約のペナルティ\n", "        max_daily_time = (8 + 2) * 60 * 2  # 昼夜2時間残業\n", "        if daily_time > max_daily_time:\n", "            total_overtime_penalty += 100000\n", "        \n", "        # 段替え回数制約のペナルティ\n", "        if daily_setup > 5:\n", "            total_setup_penalty += 1000\n", "        \n", "        # 統計値の累積\n", "        total_production_time += daily_time\n", "        total_setup_count += daily_setup\n", "        total_shortage_count += daily_shortage\n", "    \n", "    total_penalty = total_setup_penalty + total_overtime_penalty + total_shortage_penalty\n", "    return total_penalty,\n", "\n", "def generate_ind():\n", "    \"\"\"個体生成関数\"\"\"\n", "    productions = []\n", "    temp_inventory = 初期在庫量リスト[:]\n", "    \n", "    for t in range(期間):\n", "        for i in range(品番数):\n", "            shortage = max(0, 出荷数リスト[i] - temp_inventory[i])\n", "            min_production = shortage\n", "            max_production = 5000\n", "            \n", "            if min_production <= max_production:\n", "                production = random.randint(min_production, max_production)\n", "            else:\n", "                production = min_production\n", "            \n", "            productions.append(production)\n", "            \n", "            temp_inventory[i] += production - 出荷数リスト[i]\n", "    \n", "    return creator.Individual(productions)\n", "\n", "def mutate(ind):\n", "    \"\"\"突然変異関数\"\"\"\n", "    for t in range(期間):\n", "        for i in range(品番数):\n", "            if random.random() < 0.1:  # 10%の確率で突然変異\n", "                idx = t * 品番数 + i\n", "                \n", "                # 現在の生産量を基準に変更\n", "                current_production = ind[idx]\n", "                change = random.randint(-100, 100)\n", "                new_production = max(0, current_production + change)\n", "                new_production = min(5000, new_production)  # 上限制限\n", "                \n", "                ind[idx] = new_production\n", "    \n", "    return ind,\n", "\n", "def plot_results(best_individual):\n", "    \"\"\"結果をプロットする関数\"\"\"\n", "    global 品番数, 期間, 品番リスト, 出荷数リスト, サイクルタイムリスト, 込め数リスト, 初期在庫量リスト\n", "    \n", "    # 各期間の結果を計算\n", "    total_inventory_per_period = []\n", "    total_production_time_per_period = []\n", "    total_setup_times_per_period = []\n", "    total_shipment_delay_per_period = []  # 出荷遅れ時間を追加\n", "    \n", "    inventory = 初期在庫量リスト[:]\n", "    \n", "    for t in range(期間):\n", "        daily_inventory = 0\n", "        daily_production_time = 0\n", "        daily_setup_times = 0\n", "        \n", "        for i in range(品番数):\n", "            idx = t * 品番数 + i\n", "            production = best_individual[idx]\n", "            \n", "            # 在庫が十分にある場合は生産しない\n", "            if inventory[i] >= 出荷数リスト[i]:\n", "                production = 0\n", "            \n", "            # 段替え回数の計算\n", "            if production > 0:\n", "                daily_setup_times += 1\n", "            \n", "            # 生産時間の計算\n", "            if サイクルタイムリスト[i] > 0 and 込め数リスト[i] > 0:\n", "                production_time = production / (サイクルタイムリスト[i] * 込め数リスト[i])\n", "                daily_production_time += production_time\n", "            \n", "            # 在庫更新\n", "            inventory[i] += production - 出荷数リスト[i]\n", "            inventory[i] = max(0, inventory[i])  # 負の在庫は0に\n", "            \n", "            daily_inventory += inventory[i]\n", "        \n", "        total_inventory_per_period.append(daily_inventory)\n", "        total_production_time_per_period.append(daily_production_time)\n", "        total_setup_times_per_period.append(daily_setup_times)\n", "    \n", "    # プロット作成\n", "    fig, axes = plt.subplots(2, 2, figsize=(15, 10))\n", "    fig.suptitle('生産スケジューリング最適化結果', fontsize=16, fontweight='bold')\n", "    \n", "    periods = list(range(1, 期間 + 1))\n", "    \n", "    # 1. 各期間の総在庫量\n", "    axes[0, 0].bar(periods, total_inventory_per_period, color='skyblue', alpha=0.7)\n", "    axes[0, 0].set_title('各期間の総在庫量', fontweight='bold')\n", "    axes[0, 0].set_xlabel('期間')\n", "    axes[0, 0].set_ylabel('総在庫量 (個)')\n", "    axes[0, 0].grid(True, alpha=0.3)\n", "    \n", "    # 2. 各期間の総生産時間\n", "    axes[0, 1].bar(periods, total_production_time_per_period, color='lightgreen', alpha=0.7)\n", "    axes[0, 1].set_title('各期間の総生産時間', fontweight='bold')\n", "    axes[0, 1].set_xlabel('期間')\n", "    axes[0, 1].set_ylabel('総生産時間 (分)')\n", "    axes[0, 1].grid(True, alpha=0.3)\n", "    \n", "    # 3. 各期間の総段替え回数\n", "    axes[1, 0].bar(periods, total_setup_times_per_period, color='salmon', alpha=0.7)\n", "    axes[1, 0].set_title('各期間の総段替え回数', fontweight='bold')\n", "    axes[1, 0].set_xlabel('期間')\n", "    axes[1, 0].set_ylabel('段替え回数')\n", "    axes[1, 0].grid(True, alpha=0.3)\n", "    \n", "    # 段替え上限ラインを追加\n", "    axes[1, 0].axhline(y=5, color='red', linestyle='--', alpha=0.8, label='上限 (5回)')\n", "    axes[1, 0].legend()\n", "    \n", "    # 4. 統計サマリー\n", "    axes[1, 1].axis('off')\n", "    summary_text = f\"\"\"\n", "【統計サマリー】\n", "\n", "総在庫量:\n", "• 平均: {np.mean(total_inventory_per_period):.1f} 個\n", "• 最大: {np.max(total_inventory_per_period):.1f} 個\n", "• 最小: {np.min(total_inventory_per_period):.1f} 個\n", "\n", "総生産時間:\n", "• 平均: {np.mean(total_production_time_per_period):.1f} 分\n", "• 最大: {np.max(total_production_time_per_period):.1f} 分\n", "• 最小: {np.min(total_production_time_per_period):.1f} 分\n", "\n", "段替え回数:\n", "• 平均: {np.mean(total_setup_times_per_period):.1f} 回\n", "• 最大: {np.max(total_setup_times_per_period)} 回\n", "• 制約違反期間: {sum(1 for x in total_setup_times_per_period if x > 5)} 期間\n", "    \"\"\"\n", "    axes[1, 1].text(0.1, 0.9, summary_text, transform=axes[1, 1].transAxes, \n", "                    fontsize=10, verticalalignment='top', \n", "                    bbox=dict(boxstyle='round', facecolor='lightgray', alpha=0.8))\n", "    \n", "    plt.tight_layout()\n", "    plt.show()\n", "    \n", "    return total_inventory_per_period, total_production_time_per_period, total_setup_times_per_period\n", "\n", "def main():\n", "    \"\"\"メイン実行関数\"\"\"\n", "    global 品番数, 期間\n", "    \n", "    # CSVファイルの読み込み\n", "    result = read_csv('data_ga.csv')\n", "    if result[0] is None:\n", "        print(\"CSVファイルの読み込みに失敗しました\")\n", "        return\n", "    \n", "    品番数 = len(品番リスト)\n", "    期間 = 20\n", "    \n", "    # DEAPの設定\n", "    if hasattr(creator, '<PERSON><PERSON><PERSON>'):\n", "        del creator.FitnessMin\n", "    if hasattr(creator, 'Individual'):\n", "        del creator.Individual\n", "    \n", "    creator.create(\"FitnessMin\", base.Fitness, weights=(-1.0,))\n", "    creator.create(\"Individual\", list, fitness=creator.FitnessMin)\n", "    \n", "    toolbox = base.Toolbox()\n", "    toolbox.register(\"individual\", generate_ind)\n", "    toolbox.register(\"population\", tools.initRepeat, list, toolbox.individual)\n", "    toolbox.register(\"evaluate\", evaluate)\n", "    toolbox.register(\"mate\", tools.cxTwoPoint)\n", "    toolbox.register(\"mutate\", mutate)\n", "    toolbox.register(\"select\", tools.selTournament, tournsize=3)\n", "    \n", "    # GAパラメータ\n", "    population_size = 100\n", "    generations = 50\n", "    cxpb = 0.7\n", "    mutpb = 0.2\n", "    \n", "    # 初期集団の生成\n", "    population = toolbox.population(n=population_size)\n", "    hof = tools.HallOfFame(1)\n", "    stats = tools.Statistics(lambda ind: ind.fitness.values)\n", "    stats.register(\"avg\", np.mean)\n", "    stats.register(\"min\", np.min)\n", "    stats.register(\"max\", np.max)\n", "    \n", "    # GA実行\n", "    population, logbook = algorithms.eaSimple(\n", "        population, toolbox, \n", "        cxpb=cxpb, mutpb=mutpb, ngen=generations, \n", "        stats=stats, halloffame=hof, verbose=True\n", "    )\n", "    \n", "    # 結果の表示\n", "    best_ind = hof[0]\n", "    best_fitness = best_ind.fitness.values[0]\n", "    \n", "    print(f\"\\n=== 最適化結果 ===\")\n", "    print(f\"最良個体のペナルティ: {best_fitness:.2f}\")\n", "    \n", "    # 詳細な結果分析\n", "    evaluate(best_ind)\n", "    \n", "    # 結果をプロット\n", "    print(\"\\n結果をプロット中...\")\n", "    inventory_data, production_time_data, setup_data = plot_results(best_ind)\n", "    \n", "    return best_ind, logbook\n", "\n", "if __name__ == \"__main__\":\n", "    best_solution, log = main()\n"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.7"}}, "nbformat": 4, "nbformat_minor": 5}