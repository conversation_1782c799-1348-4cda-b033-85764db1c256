{"cells": [{"cell_type": "markdown", "id": "c20d8255", "metadata": {}, "source": ["これからやること  \n", "初期在庫更新式の反映  \n", "パラメータ値の実験  \n", "会社データの使用  "]}, {"cell_type": "code", "execution_count": 7, "id": "1f98fc65", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["=== GA生産スケジューリング最適化システム ===\n", "時間制約厳守版（安全在庫なし）\n", "==================================================\n", "CSV列名: ['part_number', 'shipment', 'capacity', 'cycle_time', 'cabity', 'initial_inventory']\n", "読み込み完了: 6品番\n", "=== 時間制約厳守GA（安全在庫なし） ===\n", "品番数: 6, 期間: 20\n", "1日の最大稼働時間: 1200 分\n", "時間制約厳守GA開始...\n", "gen\tnevals\tavg        \tmin       \tmax        \n", "0  \t150   \t7.37068e+09\t7.0592e+09\t7.62435e+09\n", "1  \t108   \t7.32581e+09\t6.90281e+09\t8.13355e+09\n", "2  \t114   \t7.25016e+09\t6.93025e+09\t7.7288e+09 \n", "3  \t99    \t7.17275e+09\t6.86205e+09\t7.68724e+09\n", "4  \t109   \t7.1112e+09 \t6.82996e+09\t7.91135e+09\n", "5  \t117   \t7.05535e+09\t6.69592e+09\t7.70215e+09\n", "6  \t106   \t7.00194e+09\t6.64401e+09\t7.90109e+09\n", "7  \t104   \t6.93064e+09\t6.57605e+09\t7.71233e+09\n", "8  \t107   \t6.88393e+09\t6.57605e+09\t7.66669e+09\n", "9  \t109   \t6.80135e+09\t6.46376e+09\t7.55211e+09\n", "10 \t109   \t6.75412e+09\t6.4128e+09 \t7.40594e+09\n", "11 \t107   \t6.69341e+09\t6.30335e+09\t7.37011e+09\n", "12 \t112   \t6.65225e+09\t6.26945e+09\t7.4741e+09 \n", "13 \t106   \t6.586e+09  \t6.1623e+09 \t7.36296e+09\n", "14 \t102   \t6.49037e+09\t6.1623e+09 \t7.3133e+09 \n", "15 \t102   \t6.41199e+09\t6.12401e+09\t7.31831e+09\n", "16 \t113   \t6.36999e+09\t6.08799e+09\t7.0301e+09 \n", "17 \t96    \t6.31212e+09\t6.0006e+09 \t7.15656e+09\n", "18 \t101   \t6.24328e+09\t5.86813e+09\t6.78808e+09\n", "19 \t96    \t6.21593e+09\t5.87826e+09\t6.98725e+09\n", "20 \t107   \t6.16921e+09\t5.87826e+09\t6.88761e+09\n", "21 \t108   \t6.12072e+09\t5.81801e+09\t7.16066e+09\n", "22 \t102   \t6.06756e+09\t5.73696e+09\t6.79045e+09\n", "23 \t98    \t6.0105e+09 \t5.73696e+09\t6.66232e+09\n", "24 \t109   \t5.96779e+09\t5.70618e+09\t6.74058e+09\n", "25 \t110   \t5.87498e+09\t5.65473e+09\t6.58346e+09\n", "26 \t111   \t5.81915e+09\t5.62473e+09\t6.64124e+09\n", "27 \t109   \t5.75777e+09\t5.46573e+09\t6.36391e+09\n", "28 \t103   \t5.71896e+09\t5.39274e+09\t6.449e+09  \n", "29 \t102   \t5.65163e+09\t5.38871e+09\t6.30174e+09\n", "30 \t109   \t5.58824e+09\t5.38734e+09\t6.34999e+09\n", "31 \t96    \t5.52306e+09\t5.34224e+09\t6.23248e+09\n", "32 \t106   \t5.483e+09  \t5.34224e+09\t6.23627e+09\n", "33 \t101   \t5.42247e+09\t5.31648e+09\t5.96939e+09\n", "34 \t108   \t5.41327e+09\t5.28502e+09\t6.08599e+09\n", "35 \t113   \t5.4098e+09 \t5.28428e+09\t6.16606e+09\n", "36 \t113   \t5.3781e+09 \t5.26682e+09\t6.0694e+09 \n", "37 \t121   \t5.38068e+09\t5.26682e+09\t5.97615e+09\n", "38 \t115   \t5.37748e+09\t5.25848e+09\t6.13552e+09\n", "39 \t95    \t5.33892e+09\t5.23185e+09\t5.97086e+09\n", "40 \t109   \t5.3357e+09 \t5.21424e+09\t6.0581e+09 \n", "41 \t116   \t5.32819e+09\t5.19621e+09\t5.95895e+09\n", "42 \t100   \t5.31104e+09\t5.19249e+09\t6.06699e+09\n", "43 \t95    \t5.27461e+09\t5.16961e+09\t6.00017e+09\n", "44 \t107   \t5.294e+09  \t5.16168e+09\t5.97221e+09\n", "45 \t112   \t5.25498e+09\t5.13487e+09\t5.94802e+09\n", "46 \t108   \t5.23949e+09\t5.12278e+09\t5.88604e+09\n", "47 \t108   \t5.21567e+09\t5.10031e+09\t5.84244e+09\n", "48 \t103   \t5.20904e+09\t5.10031e+09\t5.86968e+09\n", "49 \t98    \t5.16382e+09\t5.09867e+09\t5.72691e+09\n", "50 \t109   \t5.17796e+09\t5.0819e+09 \t5.89066e+09\n", "51 \t100   \t5.1657e+09 \t5.07164e+09\t6.04112e+09\n", "52 \t113   \t5.17214e+09\t5.03717e+09\t5.8614e+09 \n", "53 \t106   \t5.11093e+09\t5.03717e+09\t5.84279e+09\n", "54 \t107   \t5.12142e+09\t5.03717e+09\t5.77646e+09\n", "55 \t111   \t5.09747e+09\t5.03032e+09\t5.70257e+09\n", "56 \t116   \t5.09932e+09\t5.01072e+09\t5.73761e+09\n", "57 \t97    \t5.08191e+09\t5.01072e+09\t5.71877e+09\n", "58 \t102   \t5.07814e+09\t5.00694e+09\t5.72016e+09\n", "59 \t106   \t5.07662e+09\t4.99265e+09\t5.98489e+09\n", "60 \t112   \t5.06137e+09\t4.98426e+09\t5.98656e+09\n", "61 \t115   \t5.04404e+09\t4.98426e+09\t5.77494e+09\n", "62 \t103   \t5.05323e+09\t4.96678e+09\t5.69566e+09\n", "63 \t112   \t5.0328e+09 \t4.96678e+09\t5.80032e+09\n", "64 \t111   \t5.01653e+09\t4.95891e+09\t5.72699e+09\n", "65 \t109   \t5.02896e+09\t4.95906e+09\t5.73809e+09\n", "66 \t111   \t5.00245e+09\t4.9405e+09 \t5.91033e+09\n", "67 \t114   \t4.99774e+09\t4.9133e+09 \t5.6209e+09 \n", "68 \t115   \t5.00135e+09\t4.90965e+09\t5.74367e+09\n", "69 \t114   \t4.97957e+09\t4.9133e+09 \t5.76177e+09\n", "70 \t106   \t4.97787e+09\t4.89204e+09\t5.63288e+09\n", "71 \t114   \t4.94787e+09\t4.88333e+09\t5.61152e+09\n", "72 \t101   \t4.9488e+09 \t4.88333e+09\t5.59042e+09\n", "73 \t118   \t4.94955e+09\t4.87915e+09\t5.74801e+09\n", "74 \t112   \t4.9438e+09 \t4.86025e+09\t5.95294e+09\n", "75 \t104   \t4.91382e+09\t4.8589e+09 \t5.43451e+09\n", "76 \t112   \t4.91193e+09\t4.8589e+09 \t5.69565e+09\n", "77 \t110   \t4.90956e+09\t4.85197e+09\t5.86636e+09\n", "78 \t112   \t4.90855e+09\t4.85137e+09\t5.57966e+09\n", "79 \t110   \t4.90189e+09\t4.83374e+09\t5.6118e+09 \n", "80 \t105   \t4.87866e+09\t4.83374e+09\t5.68235e+09\n", "81 \t107   \t4.89765e+09\t4.83374e+09\t5.77249e+09\n", "82 \t103   \t4.88877e+09\t4.83374e+09\t5.63556e+09\n", "83 \t110   \t4.88522e+09\t4.82546e+09\t5.64415e+09\n", "84 \t110   \t4.89531e+09\t4.82473e+09\t5.58656e+09\n", "85 \t104   \t4.88265e+09\t4.8247e+09 \t5.65731e+09\n", "86 \t116   \t4.86771e+09\t4.8247e+09 \t5.56827e+09\n", "87 \t110   \t4.85269e+09\t4.82134e+09\t5.49567e+09\n", "88 \t107   \t4.86621e+09\t4.82038e+09\t5.48755e+09\n", "89 \t110   \t4.86865e+09\t4.81699e+09\t5.56423e+09\n", "90 \t107   \t4.8734e+09 \t4.81453e+09\t5.69271e+09\n", "91 \t101   \t4.88205e+09\t4.81324e+09\t5.72692e+09\n", "92 \t97    \t4.85231e+09\t4.81324e+09\t5.63895e+09\n", "93 \t116   \t4.88175e+09\t4.81324e+09\t5.70472e+09\n", "94 \t113   \t4.86666e+09\t4.81324e+09\t5.57135e+09\n", "95 \t92    \t4.86749e+09\t4.81324e+09\t5.77693e+09\n", "96 \t109   \t4.84441e+09\t4.81009e+09\t5.71455e+09\n", "97 \t105   \t4.86e+09   \t4.80949e+09\t5.70872e+09\n", "98 \t117   \t4.85019e+09\t4.80534e+09\t5.61051e+09\n", "99 \t113   \t4.84689e+09\t4.80534e+09\t5.56239e+09\n", "100\t102   \t4.86327e+09\t4.80351e+09\t5.72571e+09\n", "\n", "=== 最適化結果 ===\n", "最良個体のペナルティ: 4803510202.83\n", "\n", "結果をプロット中...\n"]}, {"data": {"image/png": "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******************************************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", "text/plain": ["<Figure size 1600x1200 with 4 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "=== 出荷遅れ分析 ===\n", "総出荷遅れ量: 8432.0 個\n", "出荷遅れ発生期間: 18 / 20 期間\n", "最大出荷遅れ量: 800.0 個\n", "出荷遅れ率: 90.0%\n", "時間制約違反: 13 期間\n", "段替え制約違反: 0 期間\n", "制約遵守率: 35.0%\n", "平均出荷遅れ量（遅れ発生期間のみ）: 468.4 個\n", "出荷遅れ発生期間: [3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20]\n", "\n", "==================================================\n", "最適化完了！\n", "グラフで結果を確認してください。\n"]}], "source": ["\"\"\"\n", "遺伝的アルゴリズムによる生産スケジューリング最適化\n", "時間制約厳守版 - 安全在庫なし、出荷遅れ分析付き\n", "\"\"\"\n", "\n", "import random\n", "import numpy as np\n", "import pandas as pd\n", "import csv\n", "from deap import base, creator, tools, algorithms\n", "import matplotlib.pyplot as plt\n", "import japanize_matplotlib\n", "\n", "# グローバル変数\n", "品番リスト = []\n", "出荷数リスト = []\n", "収容数リスト = []\n", "サイクルタイムリスト = []\n", "込め数リスト = []\n", "初期在庫量リスト = []\n", "品番数 = 0\n", "期間 = 20\n", "\n", "def read_csv(file_path):\n", "    \"\"\"CSVファイルを読み込む関数\"\"\"\n", "    global 品番リスト, 出荷数リスト, 収容数リスト, サイクルタイムリスト, 込め数リスト, 初期在庫量リスト\n", "    \n", "    with open(file_path, 'r', encoding='utf-8') as file:\n", "        reader = csv.reader(file)\n", "        header = next(reader)\n", "        print(f\"CSV列名: {header}\")\n", "        \n", "        品番リスト = []\n", "        出荷数リスト = []\n", "        収容数リスト = []\n", "        サイクルタイムリスト = []\n", "        込め数リスト = []\n", "        初期在庫量リスト = []\n", "        \n", "        for row in reader:\n", "            if len(row) == 0:\n", "                continue\n", "            品番リスト.append(row[header.index(\"part_number\")])\n", "            出荷数リスト.append(int(row[header.index(\"shipment\")]))\n", "            収容数リスト.append(int(row[header.index(\"capacity\")]))\n", "            サイクルタイムリスト.append(float(row[header.index(\"cycle_time\")])/60)\n", "            込め数リスト.append(int(row[header.index(\"cabity\")]))\n", "            初期在庫量リスト.append(int(row[header.index(\"initial_inventory\")]))\n", "        \n", "        print(f\"読み込み完了: {len(品番リスト)}品番\")\n", "        return 品番リスト, 出荷数リスト, 収容数リスト, サイクルタイムリスト, 込め数リスト, 初期在庫量リスト\n", "\n", "def evaluate(ind):\n", "    \"\"\"評価関数（時間制約厳守 + 強化された出荷遅れペナルティ）\"\"\"\n", "    global 品番数, 期間\n", "    \n", "    total_setup_penalty = 0\n", "    total_overtime_penalty = 0\n", "    total_shortage_penalty = 0\n", "    \n", "    inventory = 初期在庫量リスト[:]\n", "    max_daily_time = (8 + 2) * 60 * 2  # 1日の最大稼働時間（分）\n", "    \n", "    for t in range(期間):\n", "        daily_time = 0\n", "        daily_setup = 0\n", "        \n", "        for i in range(品番数):\n", "            idx = t * 品番数 + i\n", "            production = ind[idx]\n", "            \n", "            # 在庫が十分にある場合は生産しない\n", "            if inventory[i] >= 出荷数リスト[i]:\n", "                production = 0\n", "            \n", "            # 段替え時間の計算\n", "            if production > 0:\n", "                setup_time = 30\n", "                daily_setup += 1\n", "            else:\n", "                setup_time = 0\n", "            \n", "            # 生産時間の計算\n", "            if サイクルタイムリスト[i] > 0 and 込め数リスト[i] > 0:\n", "                production_time = production / (サイクルタイムリスト[i] * 込め数リスト[i])\n", "                daily_time += production_time + setup_time\n", "            \n", "            # 在庫更新\n", "            inventory[i] += production - 出荷数リスト[i]\n", "            \n", "            # 強化された出荷遅れペナルティ\n", "            if inventory[i] < 0:\n", "                shortage_amount = abs(inventory[i])\n", "                # 期間が後になるほど重いペナルティ + 不足量に比例\n", "                delay_penalty = 50000 * shortage_amount * (t + 1)\n", "                total_shortage_penalty += delay_penalty\n", "                inventory[i] = 0\n", "        \n", "        # 時間制約違反に対する非常に大きなペナルティ\n", "        if daily_time > max_daily_time:\n", "            overtime = daily_time - max_daily_time\n", "            total_overtime_penalty += 1000000 * overtime\n", "        \n", "        # 段替え制約ペナルティ\n", "        if daily_setup > 5:\n", "            total_setup_penalty += 10000 * (daily_setup - 5)\n", "    \n", "    total_penalty = total_setup_penalty + total_overtime_penalty + total_shortage_penalty\n", "    return total_penalty,\n", "\n", "def generate_ind():\n", "    \"\"\"個体生成関数（将来需要対応版 - 時間制約厳守）\"\"\"\n", "    productions = []\n", "    temp_inventory = 初期在庫量リスト[:]\n", "    max_daily_time = (8 + 2) * 60 * 2  # 1日の最大稼働時間を厳守\n", "    \n", "    for t in range(期間):\n", "        daily_time = 0\n", "        daily_productions = [0] * 品番数\n", "        \n", "        # 優先度を計算（現在+将来需要を考慮）\n", "        priorities = []\n", "        for i in range(品番数):\n", "            # 現在の不足分\n", "            current_shortage = max(0, 出荷数リスト[i] - temp_inventory[i])\n", "            \n", "            # 将来2-3期間の需要を先読み（バッファ在庫の概念）\n", "            future_periods = min(3, 期間 - t)  # 残り期間を考慮\n", "            future_demand = 出荷数リスト[i] * future_periods\n", "            \n", "            # 将来需要に対する不足リスク\n", "            future_shortage = max(0, future_demand - temp_inventory[i])\n", "            \n", "            # 総合リスク = 現在不足 + 将来不足リスク（重み付き）\n", "            total_risk = (current_shortage * 3 + future_shortage * 1) * 出荷数リスト[i]\n", "            priorities.append((total_risk, i))\n", "        \n", "        # リスクの高い順にソート\n", "        priorities.sort(reverse=True)\n", "        \n", "        # 優先度順に生産を検討（時間制約を厳守）\n", "        for risk, i in priorities:\n", "            # 現在の不足分\n", "            current_shortage = max(0, 出荷数リスト[i] - temp_inventory[i])\n", "            \n", "            # 将来需要を考慮した目標在庫レベル\n", "            future_periods = min(2, 期間 - t)  # 2期間先まで考慮\n", "            target_inventory = 出荷数リスト[i] * (1 + future_periods * 0.5)  # 現在+将来の50%\n", "            total_shortage = max(0, target_inventory - temp_inventory[i])\n", "            \n", "            # 最低でも現在不足分は生産、余裕があれば将来分も\n", "            if current_shortage > 0 or (total_shortage > 0 and temp_inventory[i] < 出荷数リスト[i] * 1.5):\n", "                setup_time = 30\n", "                remaining_time = max_daily_time - daily_time - setup_time\n", "                \n", "                if remaining_time > 0 and サイクルタイムリスト[i] > 0 and 込め数リスト[i] > 0:\n", "                    cycle_time_per_unit = サイクルタイムリスト[i] * 込め数リスト[i]\n", "                    max_producible = int(remaining_time / cycle_time_per_unit)\n", "                    \n", "                    if max_producible > 0:\n", "                        # 生産量の決定：現在不足分は必須、将来分は余裕があれば\n", "                        min_production = max(current_shortage, 出荷数リスト[i] // 2)  # 最低限\n", "                        max_production = min(total_shortage, max_producible)  # 時間制約内\n", "                        \n", "                        if min_production <= max_production:\n", "                            production = random.randint(min_production, max_production)\n", "                        else:\n", "                            production = min(min_production, max_producible)\n", "                        \n", "                        production_time = setup_time + production * cycle_time_per_unit\n", "                        \n", "                        # 時間制約を厳守（絶対に超えない）\n", "                        if daily_time + production_time <= max_daily_time:\n", "                            daily_productions[i] = production\n", "                            daily_time += production_time\n", "        \n", "        # 生産量を記録し、在庫を更新\n", "        for i in range(品番数):\n", "            productions.append(daily_productions[i])\n", "            temp_inventory[i] += daily_productions[i] - 出荷数リスト[i]\n", "            temp_inventory[i] = max(0, temp_inventory[i])\n", "    \n", "    return creator.Individual(productions)\n", "\n", "def mutate(ind):\n", "    \"\"\"突然変異関数（遅れ削減重視版）\"\"\"\n", "    max_daily_time = (8 + 2) * 60 * 2\n", "    \n", "    # 現在の解の出荷遅れを評価\n", "    temp_inventory = 初期在庫量リスト[:]\n", "    delay_periods = []\n", "    \n", "    for t in range(期間):\n", "        for i in range(品番数):\n", "            idx = t * 品番数 + i\n", "            production = ind[idx]\n", "            temp_inventory[i] += production - 出荷数リスト[i]\n", "            \n", "            if temp_inventory[i] < 0:\n", "                delay_periods.append((t, i, abs(temp_inventory[i])))\n", "                temp_inventory[i] = 0\n", "    \n", "    # 遅れが発生している期間を優先的に改善\n", "    if delay_periods:\n", "        # 最も遅れの大きい期間を選択\n", "        delay_periods.sort(key=lambda x: x[2], reverse=True)\n", "        target_period, target_product, delay_amount = delay_periods[0]\n", "        \n", "        # その期間より前の期間で生産を増やす\n", "        for t in range(max(0, target_period - 2), target_period):\n", "            idx = t * 品番数 + target_product\n", "            \n", "            # 時間制約内で生産量を増やす\n", "            if random.random() < 0.7:  # 70%の確率で改善\n", "                increase = random.randint(50, min(300, delay_amount))\n", "                ind[idx] = min(ind[idx] + increase, 2000)\n", "    \n", "    # 通常の突然変異も実行\n", "    for t in range(期間):\n", "        if random.random() < 0.08:  # 8%の確率\n", "            for i in range(品番数):\n", "                if random.random() < 0.25:\n", "                    idx = t * 品番数 + i\n", "                    change = random.randint(-80, 120)  # 増産寄り\n", "                    ind[idx] = max(0, min(2000, ind[idx] + change))\n", "    \n", "    return ind,\n", "\n", "def plot_results(best_individual):\n", "    \"\"\"結果をプロットする関数（出荷遅れ分析付き）\"\"\"\n", "    global 品番数, 期間, 品番リスト, 出荷数リスト, サイクルタイムリスト, 込め数リスト, 初期在庫量リスト\n", "\n", "    # 各期間の結果を計算\n", "    total_inventory_per_period = []\n", "    total_production_time_per_period = []\n", "    total_setup_times_per_period = []\n", "    total_shipment_delay_per_period = []\n", "\n", "    inventory = 初期在庫量リスト[:]\n", "    max_daily_time = (8 + 2) * 60 * 2\n", "\n", "    for t in range(期間):\n", "        daily_inventory = 0\n", "        daily_production_time = 0\n", "        daily_setup_times = 0\n", "        daily_shipment_delay = 0\n", "\n", "        for i in range(品番数):\n", "            idx = t * 品番数 + i\n", "            production = best_individual[idx]\n", "\n", "            # 在庫が十分にある場合は生産しない\n", "            if inventory[i] >= 出荷数リスト[i]:\n", "                production = 0\n", "\n", "            # 段替え回数の計算\n", "            if production > 0:\n", "                daily_setup_times += 1\n", "                setup_time = 30\n", "            else:\n", "                setup_time = 0\n", "\n", "            # 生産時間の計算\n", "            if サイクルタイムリスト[i] > 0 and 込め数リスト[i] > 0:\n", "                production_time = production / (サイクルタイムリスト[i] * 込め数リスト[i])\n", "                daily_production_time += production_time + setup_time\n", "\n", "            # 在庫更新と出荷遅れ計算\n", "            inventory[i] += production - 出荷数リスト[i]\n", "\n", "            # 出荷遅れの計算\n", "            if inventory[i] < 0:\n", "                daily_shipment_delay += abs(inventory[i])\n", "                inventory[i] = 0\n", "\n", "            daily_inventory += inventory[i]\n", "\n", "        total_inventory_per_period.append(daily_inventory)\n", "        total_production_time_per_period.append(daily_production_time)\n", "        total_setup_times_per_period.append(daily_setup_times)\n", "        total_shipment_delay_per_period.append(daily_shipment_delay)\n", "\n", "    # プロット作成（2x2レイアウト）\n", "    fig, axes = plt.subplots(2, 2, figsize=(16, 12))\n", "    fig.suptitle('生産スケジューリング最適化結果（時間制約厳守版）', fontsize=16, fontweight='bold')\n", "\n", "    periods = list(range(1, 期間 + 1))\n", "\n", "    # 1. 各期間の総在庫量\n", "    axes[0, 0].bar(periods, total_inventory_per_period, color='skyblue', alpha=0.7)\n", "    axes[0, 0].set_title('各期間の総在庫量', fontweight='bold')\n", "    axes[0, 0].set_xlabel('期間')\n", "    axes[0, 0].set_ylabel('総在庫量 (個)')\n", "    axes[0, 0].grid(True, alpha=0.3)\n", "\n", "    # 2. 各期間の総生産時間（制限ラインを追加）\n", "    bars_time = axes[0, 1].bar(periods, total_production_time_per_period, color='lightgreen', alpha=0.7)\n", "    axes[0, 1].axhline(y=max_daily_time, color='red', linestyle='--', alpha=0.8,\n", "                       label=f'時間制限 ({max_daily_time}分)')\n", "\n", "    # 制限を超えた期間を赤色でハイライト\n", "    for i, (period, time) in enumerate(zip(periods, total_production_time_per_period)):\n", "        if time > max_daily_time:\n", "            bars_time[i].set_color('red')\n", "\n", "    axes[0, 1].set_title('各期間の総生産時間', fontweight='bold')\n", "    axes[0, 1].set_xlabel('期間')\n", "    axes[0, 1].set_ylabel('総生産時間 (分)')\n", "    axes[0, 1].legend()\n", "    axes[0, 1].grid(True, alpha=0.3)\n", "\n", "    # 3. 各期間の総段替え回数\n", "    axes[1, 0].bar(periods, total_setup_times_per_period, color='salmon', alpha=0.7)\n", "    axes[1, 0].axhline(y=5, color='red', linestyle='--', alpha=0.8, label='上限 (5回)')\n", "    axes[1, 0].set_title('各期間の総段替え回数', fontweight='bold')\n", "    axes[1, 0].set_xlabel('期間')\n", "    axes[1, 0].set_ylabel('段替え回数')\n", "    axes[1, 0].legend()\n", "    axes[1, 0].grid(True, alpha=0.3)\n", "\n", "    # 4. 各期間の総出荷遅れ量\n", "    bars_delay = axes[1, 1].bar(periods, total_shipment_delay_per_period, color='orange', alpha=0.7)\n", "    axes[1, 1].set_title('各期間の総出荷遅れ量', fontweight='bold')\n", "    axes[1, 1].set_xlabel('期間')\n", "    axes[1, 1].set_ylabel('出荷遅れ量 (個)')\n", "    axes[1, 1].grid(True, alpha=0.3)\n", "\n", "    # 出荷遅れがある期間を赤色で強調表示\n", "    for i, (period, delay) in enumerate(zip(periods, total_shipment_delay_per_period)):\n", "        if delay > 0:\n", "            bars_delay[i].set_color('red')\n", "            bars_delay[i].set_alpha(0.8)\n", "\n", "    # 出荷遅れゼロのラインを追加\n", "    axes[1, 1].axhline(y=0, color='green', linestyle='-', alpha=0.8, label='遅れなし')\n", "    axes[1, 1].legend()\n", "\n", "    plt.tight_layout()\n", "    plt.show()\n", "\n", "    # 統計情報を出力\n", "    print(\"\\n=== 出荷遅れ分析 ===\")\n", "    total_delay = sum(total_shipment_delay_per_period)\n", "    delay_periods = sum(1 for x in total_shipment_delay_per_period if x > 0)\n", "    max_delay = max(total_shipment_delay_per_period) if total_shipment_delay_per_period else 0\n", "    time_violations = sum(1 for x in total_production_time_per_period if x > max_daily_time)\n", "    setup_violations = sum(1 for x in total_setup_times_per_period if x > 5)\n", "\n", "    print(f\"総出荷遅れ量: {total_delay:.1f} 個\")\n", "    print(f\"出荷遅れ発生期間: {delay_periods} / {期間} 期間\")\n", "    print(f\"最大出荷遅れ量: {max_delay:.1f} 個\")\n", "    print(f\"出荷遅れ率: {(delay_periods / 期間 * 100):.1f}%\")\n", "    print(f\"時間制約違反: {time_violations} 期間\")\n", "    print(f\"段替え制約違反: {setup_violations} 期間\")\n", "    print(f\"制約遵守率: {((期間 - time_violations) / 期間 * 100):.1f}%\")\n", "\n", "    if delay_periods > 0:\n", "        print(f\"平均出荷遅れ量（遅れ発生期間のみ）: {(total_delay / delay_periods):.1f} 個\")\n", "        delay_periods_list = [i+1 for i, x in enumerate(total_shipment_delay_per_period) if x > 0]\n", "        print(f\"出荷遅れ発生期間: {delay_periods_list}\")\n", "    else:\n", "        print(\"✅ 出荷遅れは発生していません！\")\n", "\n", "    return total_inventory_per_period, total_production_time_per_period, total_setup_times_per_period, total_shipment_delay_per_period\n", "\n", "def main():\n", "    \"\"\"メイン実行関数\"\"\"\n", "    global 品番数, 期間\n", "\n", "    # CSVファイルの読み込み\n", "    result = read_csv('data_ga.csv')\n", "    if result[0] is None:\n", "        print(\"CSVファイルの読み込みに失敗しました\")\n", "        return\n", "\n", "    品番数 = len(品番リスト)\n", "    期間 = 20\n", "\n", "    print(f\"=== 時間制約厳守GA（安全在庫なし） ===\")\n", "    print(f\"品番数: {品番数}, 期間: {期間}\")\n", "    print(f\"1日の最大稼働時間: {(8 + 2) * 60 * 2} 分\")\n", "\n", "    # DEAP設定\n", "    if hasattr(creator, '<PERSON><PERSON><PERSON>'):\n", "        del creator.FitnessMin\n", "    if hasattr(creator, 'Individual'):\n", "        del creator.Individual\n", "\n", "    creator.create(\"FitnessMin\", base.Fitness, weights=(-1.0,))\n", "    creator.create(\"Individual\", list, fitness=creator.FitnessMin)\n", "\n", "    toolbox = base.Toolbox()\n", "    toolbox.register(\"individual\", generate_ind)\n", "    toolbox.register(\"population\", tools.initRepeat, list, toolbox.individual)\n", "    toolbox.register(\"evaluate\", evaluate)\n", "    toolbox.register(\"mate\", tools.cxTwoPoint)\n", "    toolbox.register(\"mutate\", mutate)\n", "    toolbox.register(\"select\", tools.selTournament, tournsize=3)\n", "\n", "    # GAパラメータ（遅れ削減重視）\n", "    population_size = 150  # 集団サイズを増加\n", "    generations = 100      # 世代数を増加\n", "    cxpb = 0.6\n", "    mutpb = 0.3           # 突然変異率を増加\n", "\n", "    # 初期集団生成\n", "    population = toolbox.population(n=population_size)\n", "    hof = tools.HallOfFame(1)\n", "    stats = tools.Statistics(lambda ind: ind.fitness.values)\n", "    stats.register(\"avg\", np.mean)\n", "    stats.register(\"min\", np.min)\n", "    stats.register(\"max\", np.max)\n", "\n", "    print(\"時間制約厳守GA開始...\")\n", "\n", "    # GA実行\n", "    population, logbook = algorithms.eaSimple(\n", "        population, toolbox,\n", "        cxpb=cxpb, mutpb=mutpb, ngen=generations,\n", "        stats=stats, halloffame=hof, verbose=True\n", "    )\n", "\n", "    # 結果の表示\n", "    best_ind = hof[0]\n", "    best_fitness = best_ind.fitness.values[0]\n", "\n", "    print(f\"\\n=== 最適化結果 ===\")\n", "    print(f\"最良個体のペナルティ: {best_fitness:.2f}\")\n", "\n", "    # 結果をプロット\n", "    print(\"\\n結果をプロット中...\")\n", "    inventory_data, production_time_data, setup_data, shipment_delay_data = plot_results(best_ind)\n", "\n", "    return best_ind, logbook\n", "\n", "if __name__ == \"__main__\":\n", "    print(\"=== GA生産スケジューリング最適化システム ===\")\n", "    print(\"時間制約厳守版（安全在庫なし）\")\n", "    print(\"=\" * 50)\n", "\n", "    best_solution, log = main()\n", "\n", "    print(\"\\n\" + \"=\" * 50)\n", "    print(\"最適化完了！\")\n", "    print(\"グラフで結果を確認してください。\")\n"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.7"}}, "nbformat": 4, "nbformat_minor": 5}