"""
遺伝的アルゴリズムによる生産スケジューリング最適化
完全版 - 時間制約厳守 + 出荷遅れ分析付き
"""

import random
import numpy as np
import pandas as pd
import csv
from deap import base, creator, tools, algorithms
import matplotlib.pyplot as plt
import japanize_matplotlib

# グローバル変数の初期化
品番リスト = []
出荷数リスト = []
収容数リスト = []
サイクルタイムリスト = []
込め数リスト = []
初期在庫量リスト = []
品番数 = 0
期間 = 20

def read_csv(file_path):
    """CSVファイルを読み込む関数"""
    global 品番リスト, 出荷数リスト, 収容数リスト, サイクルタイムリスト, 込め数リスト, 初期在庫量リスト
    
    with open(file_path, 'r', encoding='utf-8') as file:
        reader = csv.reader(file)
        header = next(reader)  # ヘッダー行を読み込む
        print(f"CSV列名: {header}")
        
        品番リスト = []
        出荷数リスト = []
        収容数リスト = []
        サイクルタイムリスト = []
        込め数リスト = []
        初期在庫量リスト = []
        
        for row in reader:
            if len(row) == 0:  # 空行をスキップ
                continue
            品番リスト.append(row[header.index("part_number")])
            出荷数リスト.append(int(row[header.index("shipment")]))
            収容数リスト.append(int(row[header.index("capacity")]))
            サイクルタイムリスト.append(float(row[header.index("cycle_time")])/60)  # 分に換算
            込め数リスト.append(int(row[header.index("cabity")]))
            初期在庫量リスト.append(int(row[header.index("initial_inventory")]))
        
        print(f"読み込み完了: {len(品番リスト)}品番")
        return 品番リスト, 出荷数リスト, 収容数リスト, サイクルタイムリスト, 込め数リスト, 初期在庫量リスト

def evaluate(ind):
    """評価関数（時間制約チェック付き）"""
    global 品番数, 期間
    
    # 各期間の結果を記録するリスト
    品番在庫量 = [[] for _ in range(品番数)]
    品番生産数 = [[] for _ in range(品番数)]
    品番生産時間 = [[] for _ in range(品番数)]
    
    # ペナルティの累積値
    total_setup_penalty = 0
    total_overtime_penalty = 0
    total_shortage_penalty = 0
    
    # 統計値の累積
    total_production_time = 0
    total_setup_count = 0
    total_shortage_count = 0
    
    # 在庫の初期化
    inventory = 初期在庫量リスト[:]
    max_daily_time = (8 + 2) * 60 * 2  # 1日の最大稼働時間（分）
    
    for t in range(期間):
        daily_time = 0
        daily_setup = 0
        daily_shortage = 0
        
        for i in range(品番数):
            idx = t * 品番数 + i
            production = ind[idx]
            
            # 在庫が十分にある場合は生産しない
            if inventory[i] >= 出荷数リスト[i]:
                production = 0
            
            # 段替え時間の計算
            if production > 0:
                setup_time = 30  # 段替え時間（分）
                daily_setup += 1
            else:
                setup_time = 0
            
            # 生産時間の計算
            if サイクルタイムリスト[i] > 0 and 込め数リスト[i] > 0:
                production_time = production / (サイクルタイムリスト[i] * 込め数リスト[i])
                daily_time += production_time + setup_time
            
            # 在庫更新
            inventory[i] += production - 出荷数リスト[i]
            
            # 在庫不足のペナルティ
            if inventory[i] < 0:
                daily_shortage += abs(inventory[i])
                total_shortage_penalty += 1000 * abs(inventory[i])
                inventory[i] = 0
            
            # 結果を記録
            品番在庫量[i].append(inventory[i])
            品番生産数[i].append(production)
            品番生産時間[i].append(production_time if 'production_time' in locals() else 0)
        
        # 時間制約違反に対する厳しいペナルティ
        if daily_time > max_daily_time:
            overtime = daily_time - max_daily_time
            total_overtime_penalty += 1000000 * overtime  # 非常に大きなペナルティ
        
        # 段替え回数制約のペナルティ
        if daily_setup > 5:
            total_setup_penalty += 10000 * (daily_setup - 5)
        
        # 統計値の累積
        total_production_time += daily_time
        total_setup_count += daily_setup
        total_shortage_count += daily_shortage
    
    # 総ペナルティを返す
    total_penalty = total_setup_penalty + total_overtime_penalty + total_shortage_penalty
    return total_penalty,

def generate_ind():
    """個体生成関数（時間制約厳守版）"""
    productions = []
    temp_inventory = 初期在庫量リスト[:]
    max_daily_time = (8 + 2) * 60 * 2  # 1日の最大稼働時間（分）
    
    for t in range(期間):
        daily_time = 0
        daily_productions = [0] * 品番数
        
        # 各品番について生産量を決定（時間制約を厳守）
        for i in range(品番数):
            shortage = max(0, 出荷数リスト[i] - temp_inventory[i])
            
            if shortage > 0:  # 在庫不足がある場合のみ生産を検討
                setup_time = 30  # 段替え時間（分）
                
                # 残り時間を計算
                remaining_time = max_daily_time - daily_time - setup_time
                
                if remaining_time > 0 and サイクルタイムリスト[i] > 0 and 込め数リスト[i] > 0:
                    # 時間制約内で生産可能な最大量を計算
                    cycle_time_per_unit = サイクルタイムリスト[i] * 込め数リスト[i]
                    max_producible = int(remaining_time / cycle_time_per_unit)
                    
                    if max_producible > 0:
                        # 不足分と時間制約の範囲内で生産量を決定
                        target_production = min(shortage * 2, max_producible)
                        production = random.randint(shortage, max(shortage, target_production))
                        
                        # 実際の生産時間を計算
                        production_time = setup_time + production * cycle_time_per_unit
                        
                        # 時間制約を厳守
                        if daily_time + production_time <= max_daily_time:
                            daily_productions[i] = production
                            daily_time += production_time
        
        # 期間の生産量をリストに追加
        for i in range(品番数):
            productions.append(daily_productions[i])
            # 在庫を更新
            temp_inventory[i] += daily_productions[i] - 出荷数リスト[i]
            temp_inventory[i] = max(0, temp_inventory[i])
    
    return creator.Individual(productions)

def mutate(ind):
    """突然変異関数（時間制約対応版）"""
    max_daily_time = (8 + 2) * 60 * 2
    
    for t in range(期間):
        if random.random() < 0.1:  # 10%の確率で期間全体を変更
            # 現在の期間の生産計画を時間制約内で再生成
            daily_time = 0
            daily_productions = [0] * 品番数
            
            for i in range(品番数):
                idx = t * 品番数 + i
                current_production = ind[idx]
                
                if current_production > 0:
                    setup_time = 30
                    remaining_time = max_daily_time - daily_time - setup_time
                    
                    if remaining_time > 0 and サイクルタイムリスト[i] > 0 and 込め数リスト[i] > 0:
                        cycle_time_per_unit = サイクルタイムリスト[i] * 込め数リスト[i]
                        max_producible = int(remaining_time / cycle_time_per_unit)
                        
                        if max_producible > 0:
                            # 現在の生産量を基準に変更
                            change = random.randint(-50, 50)
                            new_production = max(0, min(max_producible, current_production + change))
                            
                            production_time = setup_time + new_production * cycle_time_per_unit
                            
                            if daily_time + production_time <= max_daily_time:
                                daily_productions[i] = new_production
                                daily_time += production_time
                                ind[idx] = new_production
    
    return ind,
