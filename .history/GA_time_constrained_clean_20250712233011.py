"""
遺伝的アルゴリズムによる生産スケジューリング最適化
時間制約厳守版 - 安全在庫なし、出荷遅れ分析付き
"""

import random
import numpy as np
import pandas as pd
import csv
from deap import base, creator, tools, algorithms
import matplotlib.pyplot as plt
import japanize_matplotlib

# グローバル変数
品番リスト = []
出荷数リスト = []
収容数リスト = []
サイクルタイムリスト = []
込め数リスト = []
初期在庫量リスト = []
品番数 = 0
期間 = 20

def read_csv(file_path):
    """CSVファイルを読み込む関数"""
    global 品番リスト, 出荷数リスト, 収容数リスト, サイクルタイムリスト, 込め数リスト, 初期在庫量リスト
    
    with open(file_path, 'r', encoding='utf-8') as file:
        reader = csv.reader(file)
        header = next(reader)
        print(f"CSV列名: {header}")
        
        品番リスト = []
        出荷数リスト = []
        収容数リスト = []
        サイクルタイムリスト = []
        込め数リスト = []
        初期在庫量リスト = []
        
        for row in reader:
            if len(row) == 0:
                continue
            品番リスト.append(row[header.index("part_number")])
            出荷数リスト.append(int(row[header.index("shipment")]))
            収容数リスト.append(int(row[header.index("capacity")]))
            サイクルタイムリスト.append(float(row[header.index("cycle_time")])/60)
            込め数リスト.append(int(row[header.index("cabity")]))
            初期在庫量リスト.append(int(row[header.index("initial_inventory")]))
        
        print(f"読み込み完了: {len(品番リスト)}品番")
        return 品番リスト, 出荷数リスト, 収容数リスト, サイクルタイムリスト, 込め数リスト, 初期在庫量リスト

def evaluate(ind):
    """評価関数（時間制約厳守 + 強化された出荷遅れペナルティ）"""
    global 品番数, 期間
    
    total_setup_penalty = 0
    total_overtime_penalty = 0
    total_shortage_penalty = 0
    
    inventory = 初期在庫量リスト[:]
    max_daily_time = (8 + 2) * 60 * 2  # 1日の最大稼働時間（分）
    
    for t in range(期間):
        daily_time = 0
        daily_setup = 0
        
        for i in range(品番数):
            idx = t * 品番数 + i
            production = ind[idx]
            
            # 在庫が十分にある場合は生産しない
            if inventory[i] >= 出荷数リスト[i]:
                production = 0
            
            # 段替え時間の計算
            if production > 0:
                setup_time = 30
                daily_setup += 1
            else:
                setup_time = 0
            
            # 生産時間の計算
            if サイクルタイムリスト[i] > 0 and 込め数リスト[i] > 0:
                production_time = production / (サイクルタイムリスト[i] * 込め数リスト[i])
                daily_time += production_time + setup_time
            
            # 在庫更新
            inventory[i] += production - 出荷数リスト[i]
            
            # 強化された出荷遅れペナルティ
            if inventory[i] < 0:
                shortage_amount = abs(inventory[i])
                # 期間が後になるほど重いペナルティ + 不足量に比例
                delay_penalty = 50000 * shortage_amount * (t + 1)
                total_shortage_penalty += delay_penalty
                inventory[i] = 0
        
        # 時間制約違反に対する非常に大きなペナルティ
        if daily_time > max_daily_time:
            overtime = daily_time - max_daily_time
            total_overtime_penalty += 1000000 * overtime
        
        # 段替え制約ペナルティ
        if daily_setup > 5:
            total_setup_penalty += 10000 * (daily_setup - 5)
    
    total_penalty = total_setup_penalty + total_overtime_penalty + total_shortage_penalty
    return total_penalty,

def generate_ind():
    """個体生成関数（時間制約厳守版）"""
    productions = []
    temp_inventory = 初期在庫量リスト[:]
    max_daily_time = (8 + 2) * 60 * 2  # 1日の最大稼働時間を厳守
    
    for t in range(期間):
        daily_time = 0
        daily_productions = [0] * 品番数
        
        # 優先度を計算（在庫不足の度合いで優先順位決定）
        priorities = []
        for i in range(品番数):
            # 在庫不足の度合いをリスクとして計算
            shortage = max(0, 出荷数リスト[i] - temp_inventory[i])
            risk = shortage * 出荷数リスト[i]  # 不足量 × 需要量
            priorities.append((risk, i))
        
        # リスクの高い順にソート
        priorities.sort(reverse=True)
        
        # 優先度順に生産を検討（時間制約を厳守）
        for risk, i in priorities:
            # 在庫不足分を計算
            shortage = max(0, 出荷数リスト[i] - temp_inventory[i])
            
            if shortage > 0:
                setup_time = 30
                remaining_time = max_daily_time - daily_time - setup_time
                
                if remaining_time > 0 and サイクルタイムリスト[i] > 0 and 込め数リスト[i] > 0:
                    cycle_time_per_unit = サイクルタイムリスト[i] * 込め数リスト[i]
                    max_producible = int(remaining_time / cycle_time_per_unit)
                    
                    if max_producible > 0:
                        # 不足分と時間制約の範囲内で生産量を決定
                        target_production = min(shortage * 2, max_producible)  # 不足分の最大2倍まで
                        production = random.randint(shortage, max(shortage, target_production))
                        production_time = setup_time + production * cycle_time_per_unit
                        
                        # 時間制約を厳守（絶対に超えない）
                        if daily_time + production_time <= max_daily_time:
                            daily_productions[i] = production
                            daily_time += production_time
        
        # 生産量を記録し、在庫を更新
        for i in range(品番数):
            productions.append(daily_productions[i])
            temp_inventory[i] += daily_productions[i] - 出荷数リスト[i]
            temp_inventory[i] = max(0, temp_inventory[i])
    
    return creator.Individual(productions)

def mutate(ind):
    """突然変異関数（時間制約対応版）"""
    max_daily_time = (8 + 2) * 60 * 2
    
    for t in range(期間):
        if random.random() < 0.1:  # 10%の確率で期間全体を変更
            for i in range(品番数):
                if random.random() < 0.3:
                    idx = t * 品番数 + i
                    
                    # 現在の生産量を基準に変更
                    current_production = ind[idx]
                    change = random.randint(-100, 100)
                    new_production = max(0, current_production + change)
                    new_production = min(2000, new_production)  # 上限制限
                    
                    ind[idx] = new_production
    
    return ind,
