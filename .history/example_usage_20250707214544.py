"""
Simple Usage Example for GA Production Scheduler

This example shows how to use the GA model for production scheduling
with your research data.
"""

from ga_production_scheduler import ProductionSchedulerGA, ProductData, SchedulingParameters
import pandas as pd


def simple_example():
    """Simple example with manual data entry"""
    print("=== Simple GA Production Scheduling Example ===")
    
    # Step 1: Define your products
    products = [
        ProductData(
            part_number="AAA",
            inventory_cost=20,      # v_i: cost per unit inventory
            manufacturing_cost=10,  # c_i: cost per unit production
            setup_cost=4,          # s_i: cost per setup
            demand=200,            # d_it: demand per period
            revenue=80,            # r_it: revenue per unit
            cycle_time=30,         # a_i: time to produce one unit
            setup_time=2000        # b_i: setup time
        ),
        ProductData("BBB", 15, 10, 4, 200, 80, 25, 2000),
        ProductData("CCC", 20, 8, 2, 100, 90, 32, 2000)
    ]
    
    # Step 2: Set scheduling parameters
    params = SchedulingParameters(
        max_load_time=8000,        # T: maximum work time per period
        max_setups=5,              # U: maximum setups per period
        min_production=1,          # epsilon: minimum production when setup
        max_production=500,        # zeta: maximum production quantity
        opportunity_loss_cost=50,  # h_i: cost of unmet demand
        num_periods=5              # n: number of time periods
    )
    
    # Step 3: Create and configure GA
    ga = ProductionSchedulerGA(products, params)
    
    # Optional: Adjust GA parameters
    ga.population_size = 100      # Number of solutions in population
    ga.max_generations = 200      # Number of evolution iterations
    ga.mutation_rate = 0.1        # Probability of mutation
    ga.crossover_rate = 0.8       # Probability of crossover
    
    # Step 4: Run optimization
    print("Starting optimization...")
    best_solution = ga.optimize(verbose=True)
    
    # Step 5: Analyze results
    analysis = ga.analyze_solution(best_solution)
    
    print(f"\n=== Optimization Results ===")
    print(f"Best fitness (profit): {analysis['total_fitness']:.2f}")
    print(f"Constraint penalties: {analysis['total_penalty']:.2f}")
    
    print(f"\n=== Cost Breakdown ===")
    costs = analysis['cost_breakdown']
    print(f"Total Revenue: {costs['total_revenue']:.2f}")
    print(f"Inventory Cost: {costs['total_inventory_cost']:.2f}")
    print(f"Manufacturing Cost: {costs['total_manufacturing_cost']:.2f}")
    print(f"Setup Cost: {costs['total_setup_cost']:.2f}")
    print(f"Opportunity Loss: {costs['total_opportunity_cost']:.2f}")
    
    # Step 6: View production schedule
    print(f"\n=== Production Schedule ===")
    for i, product in enumerate(products):
        print(f"\n{product.part_number}:")
        for t in range(params.num_periods):
            if best_solution.setup_matrix[i, t] == 1:
                qty = best_solution.production_matrix[i, t]
                inv = best_solution.inventory_matrix[i, t]
                print(f"  Period {t+1}: Produce {qty:.1f} units, Inventory: {inv:.1f}")
            else:
                inv = best_solution.inventory_matrix[i, t]
                print(f"  Period {t+1}: No production, Inventory: {inv:.1f}")
    
    # Step 7: Export results
    ga.export_schedule(best_solution, "simple_example_results")
    
    # Step 8: Plot results (optional)
    ga.plot_results(best_solution, "simple_example_plot.png")
    
    return ga, best_solution


def csv_example():
    """Example using CSV data"""
    print("\n=== CSV Data Example ===")
    
    # Load your research data
    csv_file = "お試しデータ/term1.csv"
    
    try:
        # Read CSV
        df = pd.read_csv(csv_file)
        print(f"Loaded {len(df)} products from {csv_file}")
        
        # Convert to ProductData objects
        products = []
        for _, row in df.iterrows():
            product = ProductData(
                part_number=row['part_number'],
                inventory_cost=row['inventory_cost'],
                manufacturing_cost=row['manufacturing_cost'],
                setup_cost=row['setup_cost'],
                demand=row['demand'],
                revenue=row['revenue'],
                cycle_time=row['cycle_time'],
                setup_time=row['setup_time']
            )
            products.append(product)
        
        # Set parameters appropriate for your data
        params = SchedulingParameters(
            max_load_time=15000,  # Adjust based on your capacity
            max_setups=len(products),  # Allow all products to be set up
            min_production=1,
            max_production=500,
            opportunity_loss_cost=50,
            num_periods=5
        )
        
        # Run GA
        ga = ProductionSchedulerGA(products, params)
        ga.population_size = 100
        ga.max_generations = 300
        
        best_solution = ga.optimize(verbose=True)
        
        # Analyze and export
        analysis = ga.analyze_solution(best_solution)
        
        print(f"\nFinal Results:")
        print(f"Profit: {analysis['total_fitness']:.2f}")
        print(f"Penalties: {analysis['total_penalty']:.2f}")
        
        # Export detailed results
        ga.export_schedule(best_solution, "csv_example_results")
        ga.plot_results(best_solution, "csv_example_plot.png")
        
        return ga, best_solution
        
    except FileNotFoundError:
        print(f"Could not find {csv_file}")
        print("Please make sure the CSV file exists in the correct location.")
        return None, None


def parameter_tuning_example():
    """Example of tuning GA parameters"""
    print("\n=== Parameter Tuning Example ===")
    
    # Base products
    products = [
        ProductData("AAA", 20, 10, 4, 200, 80, 30, 2000),
        ProductData("BBB", 15, 10, 4, 200, 80, 25, 2000)
    ]
    
    params = SchedulingParameters(
        max_load_time=8000,
        max_setups=4,
        num_periods=3
    )
    
    # Test different GA configurations
    configurations = [
        {"pop_size": 50, "generations": 100, "mutation": 0.05},
        {"pop_size": 100, "generations": 200, "mutation": 0.1},
        {"pop_size": 150, "generations": 300, "mutation": 0.15}
    ]
    
    best_config = None
    best_fitness = float('-inf')
    
    for i, config in enumerate(configurations):
        print(f"\nTesting configuration {i+1}: {config}")
        
        ga = ProductionSchedulerGA(products, params)
        ga.population_size = config["pop_size"]
        ga.max_generations = config["generations"]
        ga.mutation_rate = config["mutation"]
        
        solution = ga.optimize(verbose=False)
        
        print(f"Result: Fitness = {solution.fitness:.2f}")
        
        if solution.fitness > best_fitness:
            best_fitness = solution.fitness
            best_config = config
    
    print(f"\nBest configuration: {best_config}")
    print(f"Best fitness: {best_fitness:.2f}")


if __name__ == "__main__":
    # Run examples
    print("GA Production Scheduler - Usage Examples")
    print("=" * 50)
    
    # Example 1: Simple manual setup
    ga1, solution1 = simple_example()
    
    # Example 2: Using CSV data
    ga2, solution2 = csv_example()
    
    # Example 3: Parameter tuning
    parameter_tuning_example()
    
    print("\n" + "=" * 50)
    print("Examples completed!")
    print("\nCheck the generated files:")
    print("- *_results_*.csv: Production schedules")
    print("- *_plot.png: Visualization plots")
