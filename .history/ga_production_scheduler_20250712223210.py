"""
Genetic Algorithm for Production Scheduling
Based on the research formulation for multi-period production scheduling optimization.

This module implements a GA to solve the production scheduling problem with:
- Multiple products and time periods
- Inventory, manufacturing, setup, and opportunity loss costs
- Production capacity and setup time constraints
- Inventory balance equations
"""

import numpy as np
import pandas as pd
import random
import matplotlib.pyplot as plt
from typing import List, Dict, Tuple, Optional
from dataclasses import dataclass
from copy import deepcopy


@dataclass
class ProductData:
    """Data structure for product parameters"""
    part_number: str
    inventory_cost: float  # v_i: unit inventory cost
    manufacturing_cost: float  # c_i: unit manufacturing cost
    setup_cost: float  # s_i: unit setup cost
    demand: float  # d_it: demand per period
    revenue: float  # r_it: unit revenue
    cycle_time: float  # a_i: time to produce one unit
    setup_time: float  # b_i: setup time


@dataclass
class SchedulingParameters:
    """Parameters for the scheduling problem"""
    max_load_time: float = 8000  # T: maximum load time per period
    max_setups: int = 10  # U: maximum number of setups per period
    min_production: float = 1  # epsilon: minimum production quantity
    max_production: float = 1000  # zeta: maximum production quantity
    opportunity_loss_cost: float = 50  # h_i: unit opportunity loss cost
    num_periods: int = 5  # n: number of time periods


class Chromosome:
    """
    Chromosome representation for production scheduling.
    
    Encoding:
    - production_matrix: [products x periods] matrix of production quantities
    - setup_matrix: [products x periods] binary matrix indicating setups
    """
    
    def __init__(self, num_products: int, num_periods: int):
        self.num_products = num_products
        self.num_periods = num_periods
        
        # Production quantities for each product in each period
        self.production_matrix = np.zeros((num_products, num_periods))
        
        # Setup decisions (binary) for each product in each period
        self.setup_matrix = np.zeros((num_products, num_periods), dtype=int)
        
        # Inventory levels (calculated from production and demand)
        self.inventory_matrix = np.zeros((num_products, num_periods))
        
        # Opportunity loss (calculated when demand exceeds available inventory)
        self.opportunity_loss_matrix = np.zeros((num_products, num_periods))
        
        # Fitness value
        self.fitness = 0.0
        
        # Constraint violation penalty
        self.penalty = 0.0
    
    def initialize_random(self, products: List[ProductData], params: SchedulingParameters):
        """Initialize chromosome with random valid values"""
        for i in range(self.num_products):
            for t in range(self.num_periods):
                # Random setup decision (30% chance of setup)
                self.setup_matrix[i, t] = 1 if random.random() < 0.3 else 0
                
                # If setup, random production quantity
                if self.setup_matrix[i, t] == 1:
                    max_prod = min(params.max_production, 
                                 products[i].demand * 2)  # Don't overproduce too much
                    self.production_matrix[i, t] = random.uniform(
                        params.min_production, max_prod)
                else:
                    self.production_matrix[i, t] = 0
    
    def calculate_inventory_and_losses(self, products: List[ProductData], 
                                     initial_inventory: Optional[np.ndarray] = None):
        """Calculate inventory levels and opportunity losses"""
        if initial_inventory is None:
            initial_inventory = np.zeros(self.num_products)
        
        # Reset matrices
        self.inventory_matrix = np.zeros((self.num_products, self.num_periods))
        self.opportunity_loss_matrix = np.zeros((self.num_products, self.num_periods))
        
        for i in range(self.num_products):
            prev_inventory = initial_inventory[i]
            
            for t in range(self.num_periods):
                # Inventory balance: I_it = I_i(t-1) + p_it - d_it
                current_inventory = (prev_inventory + 
                                   self.production_matrix[i, t] - 
                                   products[i].demand)
                
                self.inventory_matrix[i, t] = max(0, current_inventory)
                
                # Opportunity loss: g_it = max(0, d_it - available_inventory)
                available_inventory = prev_inventory + self.production_matrix[i, t]
                self.opportunity_loss_matrix[i, t] = max(0, 
                    products[i].demand - available_inventory)
                
                prev_inventory = current_inventory
    
    def copy(self):
        """Create a deep copy of the chromosome"""
        new_chromosome = Chromosome(self.num_products, self.num_periods)
        new_chromosome.production_matrix = self.production_matrix.copy()
        new_chromosome.setup_matrix = self.setup_matrix.copy()
        new_chromosome.inventory_matrix = self.inventory_matrix.copy()
        new_chromosome.opportunity_loss_matrix = self.opportunity_loss_matrix.copy()
        new_chromosome.fitness = self.fitness
        new_chromosome.penalty = self.penalty
        return new_chromosome


class ProductionSchedulerGA:
    """
    Genetic Algorithm for Production Scheduling Optimization
    """
    
    def __init__(self, products: List[ProductData], params: SchedulingParameters):
        self.products = products
        self.params = params
        self.num_products = len(products)
        self.num_periods = params.num_periods
        
        # GA parameters
        self.population_size = 100
        self.elite_size = 10
        self.mutation_rate = 0.1
        self.crossover_rate = 0.8
        self.max_generations = 500
        
        # Population
        self.population: List[Chromosome] = []
        self.best_chromosome: Optional[Chromosome] = None
        self.fitness_history: List[float] = []
    
    def load_data_from_csv(self, csv_path: str) -> List[ProductData]:
        """Load product data from CSV file"""
        df = pd.read_csv(csv_path)
        products = []
        
        for _, row in df.iterrows():
            product = ProductData(
                part_number=row['part_number'],
                inventory_cost=row['inventory_cost'],
                manufacturing_cost=row['manufacturing_cost'],
                setup_cost=row['setup_cost'],
                demand=row['demand'],
                revenue=row['revenue'],
                cycle_time=row['cycle_time'],
                setup_time=row['setup_time']
            )
            products.append(product)
        
        return products

    def initialize_population(self):
        """Initialize the population with random chromosomes"""
        self.population = []
        for _ in range(self.population_size):
            chromosome = Chromosome(self.num_products, self.num_periods)
            chromosome.initialize_random(self.products, self.params)
            chromosome.calculate_inventory_and_losses(self.products)
            self.population.append(chromosome)

    def evaluate_fitness(self, chromosome: Chromosome) -> float:
        """
        Evaluate fitness based on the research objective function:
        max Σ(d_it * r_it - {v_i * I_it + c_i * p_it + s_i * x_it + h_i * g_it})
        """
        total_profit = 0.0
        penalty = 0.0

        for i in range(self.num_products):
            product = self.products[i]

            for t in range(self.num_periods):
                # Revenue from demand satisfaction
                revenue = product.demand * product.revenue

                # Costs
                inventory_cost = product.inventory_cost * chromosome.inventory_matrix[i, t]
                manufacturing_cost = product.manufacturing_cost * chromosome.production_matrix[i, t]
                setup_cost = product.setup_cost * chromosome.setup_matrix[i, t]
                opportunity_cost = self.params.opportunity_loss_cost * chromosome.opportunity_loss_matrix[i, t]

                period_profit = revenue - (inventory_cost + manufacturing_cost +
                                         setup_cost + opportunity_cost)
                total_profit += period_profit

        # Add constraint penalties
        penalty += self._calculate_constraint_penalties(chromosome)

        # Fitness is profit minus penalties
        fitness = total_profit - penalty
        chromosome.fitness = fitness
        chromosome.penalty = penalty

        return fitness

    def _calculate_constraint_penalties(self, chromosome: Chromosome) -> float:
        """Calculate penalties for constraint violations"""
        penalty = 0.0
        penalty_weight = 1000  # High penalty for constraint violations

        for t in range(self.num_periods):
            # Constraint 1: Total production time + setup time <= max load time
            total_time = 0.0
            total_setups = 0

            for i in range(self.num_products):
                product = self.products[i]

                # Production time
                total_time += product.cycle_time * chromosome.production_matrix[i, t]

                # Setup time
                if chromosome.setup_matrix[i, t] == 1:
                    total_time += product.setup_time
                    total_setups += 1

                # Constraint: production quantity bounds when setup
                if chromosome.setup_matrix[i, t] == 1:
                    if chromosome.production_matrix[i, t] < self.params.min_production:
                        penalty += penalty_weight * (self.params.min_production -
                                                   chromosome.production_matrix[i, t])
                    if chromosome.production_matrix[i, t] > self.params.max_production:
                        penalty += penalty_weight * (chromosome.production_matrix[i, t] -
                                                   self.params.max_production)

                # Constraint: no production without setup
                if chromosome.setup_matrix[i, t] == 0 and chromosome.production_matrix[i, t] > 0:
                    penalty += penalty_weight * chromosome.production_matrix[i, t]

            # Time constraint violation
            if total_time > self.params.max_load_time:
                penalty += penalty_weight * (total_time - self.params.max_load_time)

            # Setup count constraint violation
            if total_setups > self.params.max_setups:
                penalty += penalty_weight * (total_setups - self.params.max_setups)

        return penalty

    def selection(self) -> List[Chromosome]:
        """Tournament selection"""
        selected = []
        tournament_size = 3

        for _ in range(self.population_size):
            tournament = random.sample(self.population, tournament_size)
            winner = max(tournament, key=lambda x: x.fitness)
            selected.append(winner.copy())

        return selected

    def crossover(self, parent1: Chromosome, parent2: Chromosome) -> Tuple[Chromosome, Chromosome]:
        """Two-point crossover for production scheduling"""
        if random.random() > self.crossover_rate:
            return parent1.copy(), parent2.copy()

        child1 = parent1.copy()
        child2 = parent2.copy()

        # Crossover points
        point1 = random.randint(0, self.num_periods - 1)
        point2 = random.randint(point1, self.num_periods - 1)

        # Swap production and setup matrices between crossover points
        for i in range(self.num_products):
            for t in range(point1, point2 + 1):
                child1.production_matrix[i, t] = parent2.production_matrix[i, t]
                child1.setup_matrix[i, t] = parent2.setup_matrix[i, t]

                child2.production_matrix[i, t] = parent1.production_matrix[i, t]
                child2.setup_matrix[i, t] = parent1.setup_matrix[i, t]

        # Recalculate inventory and losses
        child1.calculate_inventory_and_losses(self.products)
        child2.calculate_inventory_and_losses(self.products)

        return child1, child2

    def mutate(self, chromosome: Chromosome):
        """Mutation operator for production scheduling"""
        for i in range(self.num_products):
            for t in range(self.num_periods):
                if random.random() < self.mutation_rate:
                    # Mutate setup decision
                    chromosome.setup_matrix[i, t] = 1 - chromosome.setup_matrix[i, t]

                    # Adjust production accordingly
                    if chromosome.setup_matrix[i, t] == 1:
                        # If setup, assign random production quantity
                        max_prod = min(self.params.max_production,
                                     self.products[i].demand * 2)
                        chromosome.production_matrix[i, t] = random.uniform(
                            self.params.min_production, max_prod)
                    else:
                        # If no setup, no production
                        chromosome.production_matrix[i, t] = 0

                elif random.random() < self.mutation_rate and chromosome.setup_matrix[i, t] == 1:
                    # Mutate production quantity if setup exists
                    current_prod = chromosome.production_matrix[i, t]
                    mutation_strength = 0.1  # 10% change

                    change = random.uniform(-mutation_strength, mutation_strength) * current_prod
                    new_prod = current_prod + change

                    # Keep within bounds
                    new_prod = max(self.params.min_production,
                                 min(self.params.max_production, new_prod))
                    chromosome.production_matrix[i, t] = new_prod

        # Recalculate inventory and losses after mutation
        chromosome.calculate_inventory_and_losses(self.products)

    def optimize(self, verbose: bool = True) -> Chromosome:
        """Main GA optimization loop"""
        # Initialize population
        self.initialize_population()

        # Evaluate initial population
        for chromosome in self.population:
            self.evaluate_fitness(chromosome)

        # Track best solution
        self.best_chromosome = max(self.population, key=lambda x: x.fitness).copy()
        self.fitness_history = [self.best_chromosome.fitness]

        if verbose:
            print(f"Generation 0: Best fitness = {self.best_chromosome.fitness:.2f}")

        # Evolution loop
        for generation in range(1, self.max_generations + 1):
            # Selection
            selected = self.selection()

            # Create new population through crossover and mutation
            new_population = []

            # Keep elite solutions
            elite = sorted(self.population, key=lambda x: x.fitness, reverse=True)[:self.elite_size]
            new_population.extend([chromosome.copy() for chromosome in elite])

            # Generate offspring
            while len(new_population) < self.population_size:
                parent1 = random.choice(selected)
                parent2 = random.choice(selected)

                child1, child2 = self.crossover(parent1, parent2)

                self.mutate(child1)
                self.mutate(child2)

                new_population.extend([child1, child2])

            # Trim to exact population size
            new_population = new_population[:self.population_size]

            # Evaluate new population
            for chromosome in new_population:
                self.evaluate_fitness(chromosome)

            self.population = new_population

            # Update best solution
            current_best = max(self.population, key=lambda x: x.fitness)
            if current_best.fitness > self.best_chromosome.fitness:
                self.best_chromosome = current_best.copy()

            self.fitness_history.append(self.best_chromosome.fitness)

            # Progress reporting
            if verbose and generation % 50 == 0:
                avg_fitness = np.mean([c.fitness for c in self.population])
                print(f"Generation {generation}: Best = {self.best_chromosome.fitness:.2f}, "
                      f"Avg = {avg_fitness:.2f}, Penalty = {self.best_chromosome.penalty:.2f}")

        if verbose:
            print(f"\nOptimization completed!")
            print(f"Best fitness: {self.best_chromosome.fitness:.2f}")
            print(f"Best penalty: {self.best_chromosome.penalty:.2f}")

        return self.best_chromosome

    def analyze_solution(self, chromosome: Chromosome) -> Dict:
        """Analyze the solution and return detailed metrics"""
        analysis = {
            'total_fitness': chromosome.fitness,
            'total_penalty': chromosome.penalty,
            'period_analysis': [],
            'product_analysis': [],
            'cost_breakdown': {
                'total_revenue': 0,
                'total_inventory_cost': 0,
                'total_manufacturing_cost': 0,
                'total_setup_cost': 0,
                'total_opportunity_cost': 0
            }
        }

        # Period-wise analysis
        for t in range(self.num_periods):
            period_data = {
                'period': t + 1,
                'total_production_time': 0,
                'total_setup_time': 0,
                'total_setups': 0,
                'products_produced': []
            }

            for i in range(self.num_products):
                product = self.products[i]

                if chromosome.setup_matrix[i, t] == 1:
                    period_data['products_produced'].append({
                        'product': product.part_number,
                        'production_qty': chromosome.production_matrix[i, t],
                        'inventory': chromosome.inventory_matrix[i, t],
                        'opportunity_loss': chromosome.opportunity_loss_matrix[i, t]
                    })

                    period_data['total_production_time'] += (
                        product.cycle_time * chromosome.production_matrix[i, t])
                    period_data['total_setup_time'] += product.setup_time
                    period_data['total_setups'] += 1

                # Cost accumulation
                analysis['cost_breakdown']['total_revenue'] += product.demand * product.revenue
                analysis['cost_breakdown']['total_inventory_cost'] += (
                    product.inventory_cost * chromosome.inventory_matrix[i, t])
                analysis['cost_breakdown']['total_manufacturing_cost'] += (
                    product.manufacturing_cost * chromosome.production_matrix[i, t])
                analysis['cost_breakdown']['total_setup_cost'] += (
                    product.setup_cost * chromosome.setup_matrix[i, t])
                analysis['cost_breakdown']['total_opportunity_cost'] += (
                    self.params.opportunity_loss_cost * chromosome.opportunity_loss_matrix[i, t])

            period_data['capacity_utilization'] = (
                (period_data['total_production_time'] + period_data['total_setup_time']) /
                self.params.max_load_time * 100)

            analysis['period_analysis'].append(period_data)

        # Product-wise analysis
        for i in range(self.num_products):
            product = self.products[i]

            total_production = np.sum(chromosome.production_matrix[i, :])
            total_demand = product.demand * self.num_periods
            total_setups = np.sum(chromosome.setup_matrix[i, :])
            avg_inventory = np.mean(chromosome.inventory_matrix[i, :])
            total_opportunity_loss = np.sum(chromosome.opportunity_loss_matrix[i, :])

            product_data = {
                'product': product.part_number,
                'total_production': total_production,
                'total_demand': total_demand,
                'demand_fulfillment_rate': min(100, (total_production / total_demand) * 100),
                'total_setups': total_setups,
                'avg_inventory': avg_inventory,
                'total_opportunity_loss': total_opportunity_loss
            }

            analysis['product_analysis'].append(product_data)

        return analysis

    def plot_results(self, chromosome: Chromosome, save_path: Optional[str] = None):
        """Plot optimization results and production schedule"""
        fig, axes = plt.subplots(2, 2, figsize=(15, 12))

        # 1. Fitness evolution
        axes[0, 0].plot(self.fitness_history)
        axes[0, 0].set_title('Fitness Evolution')
        axes[0, 0].set_xlabel('Generation')
        axes[0, 0].set_ylabel('Fitness')
        axes[0, 0].grid(True)

        # 2. Production schedule heatmap
        im = axes[0, 1].imshow(chromosome.production_matrix, cmap='YlOrRd', aspect='auto')
        axes[0, 1].set_title('Production Schedule')
        axes[0, 1].set_xlabel('Time Period')
        axes[0, 1].set_ylabel('Product')
        axes[0, 1].set_yticks(range(self.num_products))
        axes[0, 1].set_yticklabels([p.part_number for p in self.products])
        plt.colorbar(im, ax=axes[0, 1], label='Production Quantity')

        # 3. Inventory levels
        for i, product in enumerate(self.products):
            axes[1, 0].plot(range(1, self.num_periods + 1),
                          chromosome.inventory_matrix[i, :],
                          marker='o', label=product.part_number)
        axes[1, 0].set_title('Inventory Levels')
        axes[1, 0].set_xlabel('Time Period')
        axes[1, 0].set_ylabel('Inventory')
        axes[1, 0].legend()
        axes[1, 0].grid(True)

        # 4. Setup pattern
        im2 = axes[1, 1].imshow(chromosome.setup_matrix, cmap='RdYlBu', aspect='auto')
        axes[1, 1].set_title('Setup Pattern')
        axes[1, 1].set_xlabel('Time Period')
        axes[1, 1].set_ylabel('Product')
        axes[1, 1].set_yticks(range(self.num_products))
        axes[1, 1].set_yticklabels([p.part_number for p in self.products])
        plt.colorbar(im2, ax=axes[1, 1], label='Setup (1=Yes, 0=No)')

        plt.tight_layout()

        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')

        plt.show()

    def export_schedule(self, chromosome: Chromosome, filename: str):
        """Export the production schedule to CSV"""
        # Production schedule
        prod_df = pd.DataFrame(chromosome.production_matrix,
                              columns=[f'Period_{i+1}' for i in range(self.num_periods)])
        prod_df.insert(0, 'Product', [p.part_number for p in self.products])
        prod_df.to_csv(f"{filename}_production.csv", index=False)

        # Setup schedule
        setup_df = pd.DataFrame(chromosome.setup_matrix,
                               columns=[f'Period_{i+1}' for i in range(self.num_periods)])
        setup_df.insert(0, 'Product', [p.part_number for p in self.products])
        setup_df.to_csv(f"{filename}_setup.csv", index=False)

        # Inventory levels
        inv_df = pd.DataFrame(chromosome.inventory_matrix,
                             columns=[f'Period_{i+1}' for i in range(self.num_periods)])
        inv_df.insert(0, 'Product', [p.part_number for p in self.products])
        inv_df.to_csv(f"{filename}_inventory.csv", index=False)

        print(f"Schedule exported to {filename}_*.csv files")
