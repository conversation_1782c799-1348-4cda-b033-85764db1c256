"""
Test script for the GA Production Scheduler
This script demonstrates how to use the GA model with your research data.
"""

import numpy as np
import pandas as pd
from ga_production_scheduler import ProductionSchedulerGA, ProductData, SchedulingParameters
import matplotlib.pyplot as plt


def test_basic_functionality():
    """Test basic GA functionality with sample data"""
    print("=== Testing Basic GA Functionality ===")
    
    # Create sample products
    products = [
        ProductData("AAA", 20, 10, 4, 200, 80, 30, 2000),
        ProductData("BBB", 15, 10, 4, 200, 80, 25, 2000),
        ProductData("CCC", 20, 8, 2, 100, 90, 32, 2000)
    ]
    
    # Create scheduling parameters
    params = SchedulingParameters(
        max_load_time=8000,
        max_setups=10,
        min_production=1,
        max_production=1000,
        opportunity_loss_cost=50,
        num_periods=3
    )
    
    # Initialize GA
    ga = ProductionSchedulerGA(products, params)
    ga.population_size = 50  # Smaller for testing
    ga.max_generations = 100
    
    # Run optimization
    best_solution = ga.optimize(verbose=True)
    
    # Analyze results
    analysis = ga.analyze_solution(best_solution)
    
    print(f"\n=== Results ===")
    print(f"Best fitness: {analysis['total_fitness']:.2f}")
    print(f"Total penalty: {analysis['total_penalty']:.2f}")
    
    print(f"\n=== Cost Breakdown ===")
    for cost_type, value in analysis['cost_breakdown'].items():
        print(f"{cost_type}: {value:.2f}")
    
    print(f"\n=== Period Analysis ===")
    for period in analysis['period_analysis']:
        print(f"Period {period['period']}: "
              f"Capacity utilization: {period['capacity_utilization']:.1f}%, "
              f"Setups: {period['total_setups']}")
    
    return ga, best_solution


def test_with_csv_data():
    """Test GA with actual CSV data"""
    print("\n=== Testing with CSV Data ===")
    
    # Load data from CSV
    csv_path = "お試しデータ/term1.csv"
    
    try:
        df = pd.read_csv(csv_path)
        print(f"Loaded data for {len(df)} products")
        
        # Create products from CSV
        products = []
        for _, row in df.iterrows():
            product = ProductData(
                part_number=row['part_number'],
                inventory_cost=row['inventory_cost'],
                manufacturing_cost=row['manufacturing_cost'],
                setup_cost=row['setup_cost'],
                demand=row['demand'],
                revenue=row['revenue'],
                cycle_time=row['cycle_time'],
                setup_time=row['setup_time']
            )
            products.append(product)
        
        # Create parameters
        params = SchedulingParameters(
            max_load_time=15000,  # Increased for more products
            max_setups=6,  # All products
            min_production=1,
            max_production=500,
            opportunity_loss_cost=50,
            num_periods=5
        )
        
        # Initialize and run GA
        ga = ProductionSchedulerGA(products, params)
        ga.population_size = 100
        ga.max_generations = 200
        
        best_solution = ga.optimize(verbose=True)
        
        # Detailed analysis
        analysis = ga.analyze_solution(best_solution)
        
        print(f"\n=== Detailed Results ===")
        print(f"Best fitness: {analysis['total_fitness']:.2f}")
        print(f"Total penalty: {analysis['total_penalty']:.2f}")
        
        print(f"\n=== Product Performance ===")
        for product in analysis['product_analysis']:
            print(f"{product['product']}: "
                  f"Demand fulfillment: {product['demand_fulfillment_rate']:.1f}%, "
                  f"Setups: {product['total_setups']}, "
                  f"Avg inventory: {product['avg_inventory']:.1f}")
        
        # Export results
        ga.export_schedule(best_solution, "ga_results")
        
        # Plot results
        ga.plot_results(best_solution, "ga_optimization_results.png")
        
        return ga, best_solution, analysis
        
    except FileNotFoundError:
        print(f"CSV file {csv_path} not found. Please check the path.")
        return None, None, None


def compare_scenarios():
    """Compare different parameter scenarios"""
    print("\n=== Comparing Different Scenarios ===")
    
    # Base products
    products = [
        ProductData("AAA", 20, 10, 4, 200, 80, 30, 2000),
        ProductData("BBB", 15, 10, 4, 200, 80, 25, 2000),
        ProductData("CCC", 20, 8, 2, 100, 90, 32, 2000),
        ProductData("DDD", 30, 7, 2, 300, 60, 28, 2000)
    ]
    
    scenarios = [
        {"name": "High Capacity", "max_load_time": 12000, "max_setups": 8},
        {"name": "Medium Capacity", "max_load_time": 8000, "max_setups": 6},
        {"name": "Low Capacity", "max_load_time": 6000, "max_setups": 4}
    ]
    
    results = []
    
    for scenario in scenarios:
        print(f"\nTesting scenario: {scenario['name']}")
        
        params = SchedulingParameters(
            max_load_time=scenario['max_load_time'],
            max_setups=scenario['max_setups'],
            min_production=1,
            max_production=500,
            opportunity_loss_cost=50,
            num_periods=4
        )
        
        ga = ProductionSchedulerGA(products, params)
        ga.population_size = 50
        ga.max_generations = 100
        
        best_solution = ga.optimize(verbose=False)
        analysis = ga.analyze_solution(best_solution)
        
        results.append({
            'scenario': scenario['name'],
            'fitness': analysis['total_fitness'],
            'penalty': analysis['total_penalty'],
            'avg_capacity_utilization': np.mean([p['capacity_utilization'] 
                                               for p in analysis['period_analysis']])
        })
        
        print(f"Fitness: {analysis['total_fitness']:.2f}, "
              f"Penalty: {analysis['total_penalty']:.2f}")
    
    # Compare results
    print(f"\n=== Scenario Comparison ===")
    for result in results:
        print(f"{result['scenario']}: "
              f"Fitness = {result['fitness']:.2f}, "
              f"Avg Capacity = {result['avg_capacity_utilization']:.1f}%")
    
    return results


def validate_constraints():
    """Validate that the GA respects all constraints"""
    print("\n=== Constraint Validation ===")
    
    products = [
        ProductData("AAA", 20, 10, 4, 200, 80, 30, 2000),
        ProductData("BBB", 15, 10, 4, 200, 80, 25, 2000)
    ]
    
    params = SchedulingParameters(
        max_load_time=8000,
        max_setups=3,
        min_production=10,
        max_production=300,
        opportunity_loss_cost=50,
        num_periods=3
    )
    
    ga = ProductionSchedulerGA(products, params)
    ga.population_size = 30
    ga.max_generations = 50
    
    best_solution = ga.optimize(verbose=False)
    
    # Check constraints
    violations = []
    
    for t in range(params.num_periods):
        total_time = 0
        total_setups = 0
        
        for i in range(len(products)):
            product = products[i]
            
            # Check production bounds
            if best_solution.setup_matrix[i, t] == 1:
                prod_qty = best_solution.production_matrix[i, t]
                if prod_qty < params.min_production:
                    violations.append(f"Period {t+1}, Product {product.part_number}: "
                                    f"Production {prod_qty} < min {params.min_production}")
                if prod_qty > params.max_production:
                    violations.append(f"Period {t+1}, Product {product.part_number}: "
                                    f"Production {prod_qty} > max {params.max_production}")
            
            # Check no production without setup
            if (best_solution.setup_matrix[i, t] == 0 and 
                best_solution.production_matrix[i, t] > 0):
                violations.append(f"Period {t+1}, Product {product.part_number}: "
                                f"Production without setup")
            
            # Calculate time and setups
            total_time += product.cycle_time * best_solution.production_matrix[i, t]
            if best_solution.setup_matrix[i, t] == 1:
                total_time += product.setup_time
                total_setups += 1
        
        # Check time constraint
        if total_time > params.max_load_time:
            violations.append(f"Period {t+1}: Total time {total_time} > max {params.max_load_time}")
        
        # Check setup constraint
        if total_setups > params.max_setups:
            violations.append(f"Period {t+1}: Total setups {total_setups} > max {params.max_setups}")
    
    if violations:
        print("Constraint violations found:")
        for violation in violations:
            print(f"  - {violation}")
    else:
        print("All constraints satisfied!")
    
    print(f"Solution fitness: {best_solution.fitness:.2f}")
    print(f"Solution penalty: {best_solution.penalty:.2f}")
    
    return len(violations) == 0


if __name__ == "__main__":
    # Run all tests
    print("Starting GA Production Scheduler Tests")
    print("=" * 50)
    
    # Test 1: Basic functionality
    ga1, solution1 = test_basic_functionality()
    
    # Test 2: CSV data
    ga2, solution2, analysis2 = test_with_csv_data()
    
    # Test 3: Scenario comparison
    scenario_results = compare_scenarios()
    
    # Test 4: Constraint validation
    constraints_ok = validate_constraints()
    
    print(f"\n" + "=" * 50)
    print("All tests completed!")
    print(f"Constraints validation: {'PASSED' if constraints_ok else 'FAILED'}")
