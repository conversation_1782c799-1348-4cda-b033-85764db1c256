{"cells": [{"cell_type": "markdown", "id": "cbcec2c1", "metadata": {}, "source": ["## 日々スケジューリングの定式化\n", "\n", "### 定数\n", "各品番$i \\quad i = 1, \\dots, m$  \n", "各期間$t \\quad t = 1, \\dots, n$  \n", "品番$i$の単位在庫コスト$v_{i}$  \n", "品番$i$の単位製造コスト$c_{i}$  \n", "品番$i$の単位段取り替えコスト$s_{i}$  \n", "品番$i$の単位機会損失コスト$h_{i}$  \n", "期間$t$，品番$i$の機会損失量$g_{it}$  \n", "期間$t$，品番$i$の需要量$d_{it}$  \n", "期間$t$，品番$i$の単位売上$r_{it}$  \n", "品番$i$を1単位生産するための必要時間$a_{i}$  \n", "品番$i$へ段取り替えするための必要時間$b_{i}$  \n", "最大負荷時間$T$  \n", "最大段取り替え回数$U$  \n", "最小生産数を表す定数$\\epsilon$  \n", "最大生産数を表す定数$\\zeta$  \n", "\n", "### 決定変数\n", "期間$t$，品番$i$へ段取り替えするときに1を取る2値変数$x_{it} \\in {0,1}$  \n", "期間$t$，品番$i$の生産量$p_{it}$  \n", "期間$t$，品番$i$の在庫量$I_{it} = I_{i(t-1)} + p_{it} - d_{it}$  \n", "\n", "### 目的関数\n", "$\\max \\sum_{i=1}^m \\sum_{t=1}^n (d_{it}r_{it} - \\{v_{i}I_{it} + c_{i}p_{it} + s_{i}x_{it} + h_{i}g_{it} \\})$  \n", "\n", "\n", "### 制約条件\n", "(1) 総生産時間と総段取り替え時間は最大負荷時間以下  \n", "(2) 総段取り替え回数は最大段取り替え回数以下  \n", "(3) 在庫量の式  \n", "(4) 機会損失量の式  \n", "(5) 生産量は非負  \n", "(6) 段取り替えは0, 1\n", "\n", "\n", "$\\sum_{i=1}^m a_{i}p_{it} + \\sum_{i=1}^m b_{i}x_{it} \\leq T \\quad \\forall t$  \n", "$\\sum_{i=1}^m x_{it} \\leq U \\quad \\forall t$  \n", "$I_{it} = I_{i(t-1)} + p_{it} - d_{it} \\quad \\forall i, t$  \n", "$g_{it} = \\max \\{0, d_{it} - I_{it} \\} \\quad \\forall i, t$  \n", "$\\epsilon x_{it} \\leq p_{it} \\leq \\zeta x_{it} \\quad \\forall i, t$  \n", "$p_{it} \\geq 0 \\quad \\forall i, t$  \n", "$x_{it} \\in {0,1} \\quad \\forall i, t$  "]}, {"cell_type": "code", "execution_count": null, "id": "404fd61f", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.7"}}, "nbformat": 4, "nbformat_minor": 5}