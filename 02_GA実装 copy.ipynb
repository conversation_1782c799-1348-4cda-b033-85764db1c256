"""
遺伝的アルゴリズムによる生産スケジューリング最適化
"""

import random
import numpy as np
import pandas as pd
from deap import base, creator, tools, algorithms
import matplotlib.pyplot as plt
import japanize_matplotlib

# グローバル変数の初期化
品番リスト = []
出荷数リスト = []
収容数リスト = []
サイクルタイムリスト = []
込め数リスト = []
初期在庫量リスト = []
品番数 = 0
期間 = 20

def read_csv(file_path):
    """CSVファイルを読み込む関数"""
    global 品番リスト, 出荷数リスト, 収容数リスト, サイクルタイムリスト, 込め数リスト, 初期在庫量リスト
    
    try:
        df = pd.read_csv(file_path, encoding='utf-8-sig')        
        品番リスト = df['part_number'].tolist()
        出荷数リスト = df['shipment'].tolist()
        収容数リスト = df['capacity'].tolist()
        サイクルタイムリスト = (df['cycle_time'] / 60).tolist()  # 分に換算
        込め数リスト = df['cabity'].tolist()
        初期在庫量リスト = df['initial_inventory'].tolist()
        
        return 品番リスト, 出荷数リスト, 収容数リスト, サイクルタイムリスト, 込め数リスト, 初期在庫量リスト
    
    except Exception as e:
        print(f"CSVファイル読み込みエラー: {e}")
        print("ファイルパスとエンコーディングを確認してください")
        return None, None, None, None, None, None

def evaluate(ind):
    """評価関数"""
    global 品番数, 期間
    
    # 各期間の結果を記録するリスト
    品番在庫量 = [[] for _ in range(品番数)]
    品番生産数 = [[] for _ in range(品番数)]
    品番生産時間 = [[] for _ in range(品番数)]
    
    # ペナルティの累積値
    total_setup_penalty = 0
    total_overtime_penalty = 0
    total_shortage_penalty = 0
    
    # 統計値の累積
    total_production_time = 0
    total_setup_count = 0
    total_shortage_count = 0
    
    # 在庫の初期化
    inventory = 初期在庫量リスト[:]
    
    for t in range(期間):
        daily_time = 0
        daily_setup = 0
        daily_shortage = 0
        
        for i in range(品番数):
            idx = t * 品番数 + i
            production = ind[idx]
            
            # 在庫が十分にある場合は生産しない
            if inventory[i] >= 出荷数リスト[i]:
                production = 0
            
            # 段替え時間の計算
            if production > 0:
                setup_time = 1  # 段替え発生
                daily_setup += 1
            else:
                setup_time = 0
            
            # 生産時間の計算
            if サイクルタイムリスト[i] > 0 and 込め数リスト[i] > 0:
                production_time = production / (サイクルタイムリスト[i] * 込め数リスト[i])
            else:
                production_time = 0
            
            daily_time += production_time + setup_time * 30
            
            # 在庫更新
            inventory[i] += production - 出荷数リスト[i]
            
            # 在庫不足のペナルティ
            if inventory[i] < 0:
                daily_shortage += 1
                total_shortage_penalty += 1000
            
            # 結果を記録
            品番在庫量[i].append(inventory[i])
            品番生産数[i].append(production)
            品番生産時間[i].append(production_time)
        
        # 時間制約のペナルティ
        max_daily_time = (8 + 2) * 60 * 2  # 昼夜2時間残業
        if daily_time > max_daily_time:
            total_overtime_penalty += 100000
        
        # 段替え回数制約のペナルティ
        if daily_setup > 5:
            total_setup_penalty += 1000
        
        # 統計値の累積
        total_production_time += daily_time
        total_setup_count += daily_setup
        total_shortage_count += daily_shortage
    
    total_penalty = total_setup_penalty + total_overtime_penalty + total_shortage_penalty
    return total_penalty,

def generate_ind():
    """個体生成関数"""
    productions = []
    temp_inventory = 初期在庫量リスト[:]
    
    for t in range(期間):
        for i in range(品番数):
            shortage = max(0, 出荷数リスト[i] - temp_inventory[i])
            min_production = shortage
            max_production = 5000
            
            if min_production <= max_production:
                production = random.randint(min_production, max_production)
            else:
                production = min_production
            
            productions.append(production)
            
            temp_inventory[i] += production - 出荷数リスト[i]
    
    return creator.Individual(productions)

def mutate(ind):
    """突然変異関数"""
    for t in range(期間):
        for i in range(品番数):
            if random.random() < 0.1:  # 10%の確率で突然変異
                idx = t * 品番数 + i
                
                # 現在の生産量を基準に変更
                current_production = ind[idx]
                change = random.randint(-100, 100)
                new_production = max(0, current_production + change)
                new_production = min(5000, new_production)  # 上限制限
                
                ind[idx] = new_production
    
    return ind,

def main():
    """メイン実行関数"""
    global 品番数, 期間
    
    # CSVファイルの読み込み
    result = read_csv('data_ga.csv')
    if result[0] is None:
        print("CSVファイルの読み込みに失敗しました")
        return
    
    品番数 = len(品番リスト)
    期間 = 20
    
    # DEAPの設定
    if hasattr(creator, 'FitnessMin'):
        del creator.FitnessMin
    if hasattr(creator, 'Individual'):
        del creator.Individual
    
    creator.create("FitnessMin", base.Fitness, weights=(-1.0,))
    creator.create("Individual", list, fitness=creator.FitnessMin)
    
    toolbox = base.Toolbox()
    toolbox.register("individual", generate_ind)
    toolbox.register("population", tools.initRepeat, list, toolbox.individual)
    toolbox.register("evaluate", evaluate)
    toolbox.register("mate", tools.cxTwoPoint)
    toolbox.register("mutate", mutate)
    toolbox.register("select", tools.selTournament, tournsize=3)
    
    # GAパラメータ
    population_size = 100
    generations = 50
    cxpb = 0.7
    mutpb = 0.2
    
    # 初期集団の生成
    population = toolbox.population(n=population_size)
    hof = tools.HallOfFame(1)
    stats = tools.Statistics(lambda ind: ind.fitness.values)
    stats.register("avg", np.mean)
    stats.register("min", np.min)
    stats.register("max", np.max)
    
    # GA実行
    population, logbook = algorithms.eaSimple(
        population, toolbox, 
        cxpb=cxpb, mutpb=mutpb, ngen=generations, 
        stats=stats, halloffame=hof, verbose=True
    )
    
    # 結果の表示
    best_ind = hof[0]
    best_fitness = best_ind.fitness.values[0]
    
    print(f"\n=== 最適化結果 ===")
    print(f"最良個体のペナルティ: {best_fitness:.2f}")
    
    # 詳細な結果分析
    evaluate(best_ind)
    
    return best_ind, logbook

if __name__ == "__main__":
    best_solution, log = main()
