import random
import numpy as np
import csv
import pandas as pd
from deap import base, creator, tools, algorithms
import matplotlib.pyplot as plt
import japanize_matplotlib


def read_csv(file_path):
    with open(file_path, 'r') as file:
        reader = csv.reader(file)
        品番リスト = [row["part_number"] for row in reader]
        出荷数リスト = [row["shipment"] for row in reader]
        収容数リスト = [row["capacity"] for row in reader]
        サイクルタイムリスト = [row["cycle_time"] for row in reader]
        込め数リスト = [row["cabity"] for row in reader]
        初期在庫量リスト = [row["initial_inventory"] for row in reader]
        
    return 品番リスト, 出荷数リスト, 収容数リスト, サイクルタイムリスト, 込め数リスト, 初期在庫量リスト

def evaluate(ind):
    global 品番在庫量, 品番生産数, 品番生産時間, 総段替え数, 総生産時間, 出荷遅れ件数
    
    品番在庫量 = [[] for _ in range(品番数)]
    品番生産数 = [[] for _ in range(品番数)]
    品番生産時間 = [[] for _ in range(品番数)]
    総生産時間 = 0
    総段替え数 = 0
    出荷遅れ件数 = 0
    
    setup_penalty = 0
    overtime_penalty = 0
    shortage_penalty = 0
    
    inventory = 初期在庫量リスト[:] #初期在庫量
    
    for t in range(期間):
        daily_time = 0
        daily_setup = 0
        daily_shortage = 0
        
        for i in range(品番数):
            idx = t * 品番数 + i
            production = ind[idx]
            
            if inventory[i] >= 出荷数リスト[t][i]: #在庫がある場合は生産しない
                production = 0
            
            if production > 0: #生産数がある場合に段替え発生
                setup_time = 1
                daily_setup += 1
            else:
                setup_time = 0
            
            production_time = production / サイクルタイムリスト[i] / 込め数リスト[i]
            daily_time += production_time + setup_time * 30
            inventory[i] += production - 出荷数リスト[i]
            
            if inventory[i] < 0: #在庫が切れた場合はペナルティ
                daily_shortage += 1
                shortage_penalty += 1000
            
            品番在庫量[i].append(inventory[i])
            品番生産数[i].append(production)
            品番生産時間[i].append(production_time)
            
        if daily_time > (8+2) * 60 * 2: #昼夜2時間残業の稼働を超える場合はペナルティ
            overtime_penalty += 100000
        
        if daily_setup > 5: #段替え回数が5回を超える場合はペナルティ
            setup_penalty += 1000
        
        総生産時間 = daily_time
        総段替え数 = daily_setup * 30
        出荷遅れ件数 = daily_shortage
    
    return setup_penalty + overtime_penalty + shortage_penalty,

def generate_ind():
    productions = []
    
    for t in range(期間):
        for i in range(品番数):
            min_production = max(0, 出荷数リスト[i] - 品番在庫量[i])
            production = random.randint(min_production, 5000)
            productions.append(production)
            
    return creator.Individual(productions)

def mutate(ind):
    for t in range(期間):
        for i in range(品番数):
            idx = t * 品番数 + i
            min_production = max(0, 出荷数リスト[i] - 品番在庫量[i])
            production = random.randint(min_production, 5000)
            ind[idx] = production
            
    return ind,

#設定値
population_size = 100
generations = 50
cxpb = 0.7
mutpb = 0.2

品番リスト, 出荷数リスト, 収容数リスト, サイクルタイムリスト, 込め数リスト, 初期在庫量リスト = read_csv('data_ga.csv')
品番数 = len(品番リスト)
期間 = 20

creator.create("FitnessMin", base.Fitness, weights=(-1.0,))
creator.create("Individual", list, fitness=creator.FitnessMin)

toolbox = base.Toolbox()
toolbox.register("individual", generate_ind)
toolbox.register("population", tools.initRepeat, list, toolbox.individual)
toolbox.register("evaluate", evaluate)
toolbox.register("mate", tools.cxTwoPoint)
toolbox.register("mutate", mutate)
toolbox.register("select", tools.selTournament, tournsize=3)

population = toolbox.population(n=population_size)
hof = tools.HallOfFame(1)
stats = tools.Statistics(lambda ind: ind.fitness.values)
stats.register("avg", np.mean)
stats.register("min", np.min)
stats.register("max", np.max)

population, logbook = algorithms.eaSimple(population, toolbox, cxpb=cxpb, mutpb=mutpb, ngen=generations, stats=stats, halloffame=hof, verbose=True)

def main():
    global 品番在庫量, 品番生産数, 品番生産時間, 総段替え数, 総生産時間, 出荷遅れ件数
    
    品番在庫量 = [[] for _ in range(品番数)]
    品番生産数 = [[] for _ in range(品番数)]
    品番生産時間 = [[] for _ in range(品番数)]
    総生産時間 = 0
    総段替え数 = 0
    出荷遅れ件数 = 0
    
    best_ind = hof[0]
    evaluate(best_ind)
    
    print("Best individual:", best_ind)
    print("Total production time:", 総生産時間)
    print("Total setup time:", 総段替え数)
    print("Total shortage:", 出荷遅れ件数)
    
if __name__ == "__main__":
    main()

