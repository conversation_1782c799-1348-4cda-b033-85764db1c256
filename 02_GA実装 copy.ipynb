"""
遺伝的アルゴリズムによる生産スケジューリング最適化
完全版 - 時間制約厳守 + 出荷遅れ分析付き
"""

import random
import numpy as np
import pandas as pd
import csv
from deap import base, creator, tools, algorithms
import matplotlib.pyplot as plt
import japanize_matplotlib

# グローバル変数の初期化
品番リスト = []
出荷数リスト = []
収容数リスト = []
サイクルタイムリスト = []
込め数リスト = []
初期在庫量リスト = []
品番数 = 0
期間 = 20

def read_csv(file_path):
    """CSVファイルを読み込む関数"""
    global 品番リスト, 出荷数リスト, 収容数リスト, サイクルタイムリスト, 込め数リスト, 初期在庫量リスト
    
    with open(file_path, 'r', encoding='utf-8') as file:
        reader = csv.reader(file)
        header = next(reader)  # ヘッダー行を読み込む
        print(f"CSV列名: {header}")
        
        品番リスト = []
        出荷数リスト = []
        収容数リスト = []
        サイクルタイムリスト = []
        込め数リスト = []
        初期在庫量リスト = []
        
        for row in reader:
            if len(row) == 0:  # 空行をスキップ
                continue
            品番リスト.append(row[header.index("part_number")])
            出荷数リスト.append(int(row[header.index("shipment")]))
            収容数リスト.append(int(row[header.index("capacity")]))
            サイクルタイムリスト.append(float(row[header.index("cycle_time")])/60)  # 分に換算
            込め数リスト.append(int(row[header.index("cabity")]))
            初期在庫量リスト.append(int(row[header.index("initial_inventory")]))
        
        print(f"読み込み完了: {len(品番リスト)}品番")
        return 品番リスト, 出荷数リスト, 収容数リスト, サイクルタイムリスト, 込め数リスト, 初期在庫量リスト

def evaluate(ind):
    """評価関数（時間制約チェック付き）"""
    global 品番数, 期間
    
    # 各期間の結果を記録するリスト
    品番在庫量 = [[] for _ in range(品番数)]
    品番生産数 = [[] for _ in range(品番数)]
    品番生産時間 = [[] for _ in range(品番数)]
    
    # ペナルティの累積値
    total_setup_penalty = 0
    total_overtime_penalty = 0
    total_shortage_penalty = 0
    
    # 統計値の累積
    total_production_time = 0
    total_setup_count = 0
    total_shortage_count = 0
    
    # 在庫の初期化
    inventory = 初期在庫量リスト[:]
    max_daily_time = (8 + 2) * 60 * 2  # 1日の最大稼働時間（分）
    
    for t in range(期間):
        daily_time = 0
        daily_setup = 0
        daily_shortage = 0
        
        for i in range(品番数):
            idx = t * 品番数 + i
            production = ind[idx]
            
            # 在庫が十分にある場合は生産しない
            if inventory[i] >= 出荷数リスト[i]:
                production = 0
            
            # 段替え時間の計算
            if production > 0:
                setup_time = 30  # 段替え時間（分）
                daily_setup += 1
            else:
                setup_time = 0
            
            # 生産時間の計算
            if サイクルタイムリスト[i] > 0 and 込め数リスト[i] > 0:
                production_time = production / (サイクルタイムリスト[i] * 込め数リスト[i])
                daily_time += production_time + setup_time
            
            # 在庫更新
            inventory[i] += production - 出荷数リスト[i]
            
            # 在庫不足のペナルティ
            if inventory[i] < 0:
                daily_shortage += abs(inventory[i])
                total_shortage_penalty += 1000 * abs(inventory[i])
                inventory[i] = 0
            
            # 結果を記録
            品番在庫量[i].append(inventory[i])
            品番生産数[i].append(production)
            品番生産時間[i].append(production_time if 'production_time' in locals() else 0)
        
        # 時間制約違反に対する厳しいペナルティ
        if daily_time > max_daily_time:
            overtime = daily_time - max_daily_time
            total_overtime_penalty += 1000000 * overtime  # 非常に大きなペナルティ
        
        # 段替え回数制約のペナルティ
        if daily_setup > 5:
            total_setup_penalty += 10000 * (daily_setup - 5)
        
        # 統計値の累積
        total_production_time += daily_time
        total_setup_count += daily_setup
        total_shortage_count += daily_shortage
    
    # 総ペナルティを返す
    total_penalty = total_setup_penalty + total_overtime_penalty + total_shortage_penalty
    return total_penalty,

def generate_ind():
    """個体生成関数（時間制約厳守版）"""
    productions = []
    temp_inventory = 初期在庫量リスト[:]
    max_daily_time = (8 + 2) * 60 * 2  # 1日の最大稼働時間（分）
    
    for t in range(期間):
        daily_time = 0
        daily_productions = [0] * 品番数
        
        # 各品番について生産量を決定（時間制約を厳守）
        for i in range(品番数):
            shortage = max(0, 出荷数リスト[i] - temp_inventory[i])
            
            if shortage > 0:  # 在庫不足がある場合のみ生産を検討
                setup_time = 30  # 段替え時間（分）
                
                # 残り時間を計算
                remaining_time = max_daily_time - daily_time - setup_time
                
                if remaining_time > 0 and サイクルタイムリスト[i] > 0 and 込め数リスト[i] > 0:
                    # 時間制約内で生産可能な最大量を計算
                    cycle_time_per_unit = サイクルタイムリスト[i] * 込め数リスト[i]
                    max_producible = int(remaining_time / cycle_time_per_unit)
                    
                    if max_producible > 0:
                        # 不足分と時間制約の範囲内で生産量を決定
                        target_production = min(shortage * 2, max_producible)
                        production = random.randint(shortage, max(shortage, target_production))
                        
                        # 実際の生産時間を計算
                        production_time = setup_time + production * cycle_time_per_unit
                        
                        # 時間制約を厳守
                        if daily_time + production_time <= max_daily_time:
                            daily_productions[i] = production
                            daily_time += production_time
        
        # 期間の生産量をリストに追加
        for i in range(品番数):
            productions.append(daily_productions[i])
            # 在庫を更新
            temp_inventory[i] += daily_productions[i] - 出荷数リスト[i]
            temp_inventory[i] = max(0, temp_inventory[i])
    
    return creator.Individual(productions)

def mutate(ind):
    """突然変異関数（時間制約対応版）"""
    max_daily_time = (8 + 2) * 60 * 2
    
    for t in range(期間):
        if random.random() < 0.1:  # 10%の確率で期間全体を変更
            # 現在の期間の生産計画を時間制約内で再生成
            daily_time = 0
            daily_productions = [0] * 品番数
            
            for i in range(品番数):
                idx = t * 品番数 + i
                current_production = ind[idx]
                
                if current_production > 0:
                    setup_time = 30
                    remaining_time = max_daily_time - daily_time - setup_time
                    
                    if remaining_time > 0 and サイクルタイムリスト[i] > 0 and 込め数リスト[i] > 0:
                        cycle_time_per_unit = サイクルタイムリスト[i] * 込め数リスト[i]
                        max_producible = int(remaining_time / cycle_time_per_unit)
                        
                        if max_producible > 0:
                            # 現在の生産量を基準に変更
                            change = random.randint(-50, 50)
                            new_production = max(0, min(max_producible, current_production + change))
                            
                            production_time = setup_time + new_production * cycle_time_per_unit
                            
                            if daily_time + production_time <= max_daily_time:
                                daily_productions[i] = new_production
                                daily_time += production_time
                                ind[idx] = new_production
    
    return ind,

def plot_results(best_individual):
    """結果をプロットする関数（出荷遅れ分析付き）"""
    global 品番数, 期間, 品番リスト, 出荷数リスト, サイクルタイムリスト, 込め数リスト, 初期在庫量リスト

    # 各期間の結果を計算
    total_inventory_per_period = []
    total_production_time_per_period = []
    total_setup_times_per_period = []
    total_shipment_delay_per_period = []  # 出荷遅れ時間を追加

    inventory = 初期在庫量リスト[:]
    max_daily_time = (8 + 2) * 60 * 2  # 1日の最大稼働時間

    for t in range(期間):
        daily_inventory = 0
        daily_production_time = 0
        daily_setup_times = 0
        daily_shipment_delay = 0  # 出荷遅れ量を追加

        for i in range(品番数):
            idx = t * 品番数 + i
            production = best_individual[idx]

            # 在庫が十分にある場合は生産しない
            if inventory[i] >= 出荷数リスト[i]:
                production = 0

            # 段替え回数の計算
            if production > 0:
                daily_setup_times += 1
                setup_time = 30
            else:
                setup_time = 0

            # 生産時間の計算
            if サイクルタイムリスト[i] > 0 and 込め数リスト[i] > 0:
                production_time = production / (サイクルタイムリスト[i] * 込め数リスト[i])
                daily_production_time += production_time + setup_time

            # 在庫更新と出荷遅れ計算
            inventory[i] += production - 出荷数リスト[i]

            # 出荷遅れの計算
            if inventory[i] < 0:
                daily_shipment_delay += abs(inventory[i])  # 不足分が出荷遅れ
                inventory[i] = 0  # 負の在庫は0に

            daily_inventory += inventory[i]

        total_inventory_per_period.append(daily_inventory)
        total_production_time_per_period.append(daily_production_time)
        total_setup_times_per_period.append(daily_setup_times)
        total_shipment_delay_per_period.append(daily_shipment_delay)

    # プロット作成（2x2レイアウト）
    fig, axes = plt.subplots(2, 2, figsize=(16, 12))
    fig.suptitle('生産スケジューリング最適化結果（時間制約厳守版）', fontsize=16, fontweight='bold')

    periods = list(range(1, 期間 + 1))

    # 1. 各期間の総在庫量
    axes[0, 0].bar(periods, total_inventory_per_period, color='skyblue', alpha=0.7)
    axes[0, 0].set_title('各期間の総在庫量', fontweight='bold')
    axes[0, 0].set_xlabel('期間')
    axes[0, 0].set_ylabel('総在庫量 (個)')
    axes[0, 0].grid(True, alpha=0.3)

    # 2. 各期間の総生産時間（制限ラインを追加）
    bars_time = axes[0, 1].bar(periods, total_production_time_per_period, color='lightgreen', alpha=0.7)
    axes[0, 1].axhline(y=max_daily_time, color='red', linestyle='--', alpha=0.8,
                       label=f'時間制限 ({max_daily_time}分)')

    # 制限を超えた期間を赤色でハイライト
    for i, (period, time) in enumerate(zip(periods, total_production_time_per_period)):
        if time > max_daily_time:
            bars_time[i].set_color('red')

    axes[0, 1].set_title('各期間の総生産時間', fontweight='bold')
    axes[0, 1].set_xlabel('期間')
    axes[0, 1].set_ylabel('総生産時間 (分)')
    axes[0, 1].legend()
    axes[0, 1].grid(True, alpha=0.3)

    # 3. 各期間の総段替え回数
    axes[1, 0].bar(periods, total_setup_times_per_period, color='salmon', alpha=0.7)
    axes[1, 0].axhline(y=5, color='red', linestyle='--', alpha=0.8, label='上限 (5回)')
    axes[1, 0].set_title('各期間の総段替え回数', fontweight='bold')
    axes[1, 0].set_xlabel('期間')
    axes[1, 0].set_ylabel('段替え回数')
    axes[1, 0].legend()
    axes[1, 0].grid(True, alpha=0.3)

    # 4. 各期間の総出荷遅れ量
    bars_delay = axes[1, 1].bar(periods, total_shipment_delay_per_period, color='orange', alpha=0.7)
    axes[1, 1].set_title('各期間の総出荷遅れ量', fontweight='bold')
    axes[1, 1].set_xlabel('期間')
    axes[1, 1].set_ylabel('出荷遅れ量 (個)')
    axes[1, 1].grid(True, alpha=0.3)

    # 出荷遅れがある期間を赤色で強調表示
    for i, (period, delay) in enumerate(zip(periods, total_shipment_delay_per_period)):
        if delay > 0:
            bars_delay[i].set_color('red')
            bars_delay[i].set_alpha(0.8)

    # 出荷遅れゼロのラインを追加
    axes[1, 1].axhline(y=0, color='green', linestyle='-', alpha=0.8, label='遅れなし')
    axes[1, 1].legend()

    plt.tight_layout()
    plt.show()

    # 統計情報を出力
    print("\n=== 出荷遅れ分析 ===")
    total_delay = sum(total_shipment_delay_per_period)
    delay_periods = sum(1 for x in total_shipment_delay_per_period if x > 0)
    max_delay = max(total_shipment_delay_per_period) if total_shipment_delay_per_period else 0
    time_violations = sum(1 for x in total_production_time_per_period if x > max_daily_time)
    setup_violations = sum(1 for x in total_setup_times_per_period if x > 5)

    print(f"総出荷遅れ量: {total_delay:.1f} 個")
    print(f"出荷遅れ発生期間: {delay_periods} / {期間} 期間")
    print(f"最大出荷遅れ量: {max_delay:.1f} 個")
    print(f"出荷遅れ率: {(delay_periods / 期間 * 100):.1f}%")
    print(f"時間制約違反: {time_violations} 期間")
    print(f"段替え制約違反: {setup_violations} 期間")
    print(f"制約遵守率: {((期間 - time_violations) / 期間 * 100):.1f}%")

    if delay_periods > 0:
        print(f"平均出荷遅れ量（遅れ発生期間のみ）: {(total_delay / delay_periods):.1f} 個")
        delay_periods_list = [i+1 for i, x in enumerate(total_shipment_delay_per_period) if x > 0]
        print(f"出荷遅れ発生期間: {delay_periods_list}")
    else:
        print("✅ 出荷遅れは発生していません！")

    return total_inventory_per_period, total_production_time_per_period, total_setup_times_per_period, total_shipment_delay_per_period

def main():
    """メイン実行関数"""
    global 品番数, 期間

    # CSVファイルの読み込み
    result = read_csv('data_ga.csv')
    if result[0] is None:
        print("CSVファイルの読み込みに失敗しました")
        return

    品番数 = len(品番リスト)
    期間 = 20

    print(f"品番数: {品番数}, 期間: {期間}")
    print(f"1日の最大稼働時間: {(8 + 2) * 60 * 2} 分")

    # DEAPの設定
    if hasattr(creator, 'FitnessMin'):
        del creator.FitnessMin
    if hasattr(creator, 'Individual'):
        del creator.Individual

    creator.create("FitnessMin", base.Fitness, weights=(-1.0,))
    creator.create("Individual", list, fitness=creator.FitnessMin)

    toolbox = base.Toolbox()
    toolbox.register("individual", generate_ind)
    toolbox.register("population", tools.initRepeat, list, toolbox.individual)
    toolbox.register("evaluate", evaluate)
    toolbox.register("mate", tools.cxTwoPoint)
    toolbox.register("mutate", mutate)
    toolbox.register("select", tools.selTournament, tournsize=3)

    # GAパラメータ
    population_size = 100
    generations = 50
    cxpb = 0.7
    mutpb = 0.2

    # 初期集団の生成
    population = toolbox.population(n=population_size)
    hof = tools.HallOfFame(1)
    stats = tools.Statistics(lambda ind: ind.fitness.values)
    stats.register("avg", np.mean)
    stats.register("min", np.min)
    stats.register("max", np.max)

    print("遺伝的アルゴリズム開始（時間制約厳守版）...")

    # GA実行
    population, logbook = algorithms.eaSimple(
        population, toolbox,
        cxpb=cxpb, mutpb=mutpb, ngen=generations,
        stats=stats, halloffame=hof, verbose=True
    )

    # 結果の表示
    best_ind = hof[0]
    best_fitness = best_ind.fitness.values[0]

    print(f"\n=== 最適化結果 ===")
    print(f"最良個体のペナルティ: {best_fitness:.2f}")

    # 詳細な結果分析
    evaluate(best_ind)

    # 結果をプロット
    print("\n結果をプロット中...")
    inventory_data, production_time_data, setup_data, shipment_delay_data = plot_results(best_ind)

    return best_ind, logbook

if __name__ == "__main__":
    print("=== GA生産スケジューリング最適化システム ===")
    print("時間制約厳守版 + 出荷遅れ分析付き")
    print("=" * 50)

    best_solution, log = main()

    print("\n" + "=" * 50)
    print("最適化完了！")
    print("グラフで結果を確認してください。")
