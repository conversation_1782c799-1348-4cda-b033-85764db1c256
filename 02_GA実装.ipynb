import random
import numpy as np
import pandas as pd
from deap import base, creator, tools, algorithms
import matplotlib.pyplot as plt
import japanize_matplotlib
import seaborn as sns
from functools import partial

# DEAPの設定はグローバルで行う
creator.create("FitnessMin", base.Fitness, weights=(-1.0,))
creator.create("Individual", list, fitness=creator.FitnessMin)

toolbox = base.Toolbox()

# --- 評価関数 ---
# 評価関数はデータや期間、ペナルティなどのパラメータに依存するため、引数として受け取る
def evaluate(individual, data_dict, num_days, regular_work_time_sec, overtime_cost_per_sec, 
             penalty_over_setup_limit, penalty_demand_shortage, penalty_deviation_from_average_production, part_numbers_list):
    
    total_inventory_cost = 0
    total_overtime_cost = 0
    total_setup_cost = 0
    total_penalty_setup_limit = 0
    total_penalty_demand_shortage = 0
    
    daily_production_quantities = {part: [0] * num_days for part in part_numbers_list}
    current_inventory = {part: 0 for part in part_numbers_list}

    daily_setup_counts_eval = [0] * num_days

    for day_idx, day_schedule in enumerate(individual):
        daily_work_time_sec = 0
        produced_today = {part: 0 for part in part_numbers_list}
        parts_scheduled_today = {part for part, _ in day_schedule}

        for part, shots in day_schedule:
            part_data = data_dict[part]
            production_quantity = shots * part_data["cabity"]
            daily_work_time_sec += shots * part_data["cycle_time_per_shot"]
            produced_today[part] += production_quantity

        daily_setup_count = len(parts_scheduled_today)
        daily_setup_counts_eval[day_idx] = daily_setup_count
        if part_numbers_list: # データが空でないことを確認
            total_setup_cost += daily_setup_count * data_dict[part_numbers_list[0]]["setup_cost"] 

        if daily_work_time_sec > regular_work_time_sec:
            overtime_sec = daily_work_time_sec - regular_work_time_sec
            total_overtime_cost += overtime_sec * overtime_cost_per_sec

        if daily_setup_count > 3:
            total_penalty_setup_limit += penalty_over_setup_limit * (daily_setup_count - 3)

        for part in part_numbers_list:
            part_data = data_dict[part]
            demand_today = part_data["demand_per_day"]
            
            current_inventory[part] += produced_today[part]
            
            if current_inventory[part] >= demand_today:
                current_inventory[part] -= demand_today
            else:
                shortage = demand_today - current_inventory[part]
                total_penalty_demand_shortage += penalty_demand_shortage * shortage
                current_inventory[part] = 0

            num_baskets_in_stock = np.ceil(max(0, current_inventory[part]) / part_data["capacity_par_basket"])
            total_inventory_cost += num_baskets_in_stock * part_data["inventory_cost_pet_basket"]
            
            daily_production_quantities[part][day_idx] = produced_today[part]

    total_penalty_leveling = 0
    for part in part_numbers_list:
        daily_quantities = np.array(daily_production_quantities[part])
        if np.sum(daily_quantities) > 0:
            total_penalty_leveling += np.std(daily_quantities) * penalty_deviation_from_average_production

    total_cost = (total_inventory_cost + total_overtime_cost + total_setup_cost + 
                  total_penalty_setup_limit + total_penalty_demand_shortage + 
                  total_penalty_leveling)
    
    return total_cost,

# --- プロット関数 ---
# プロット関数もデータや期間、定時稼働時間、品番リスト、型替え回数リストを引数として受け取る
def plot_results(best_individual, data_dict, num_days, regular_work_time_sec, part_numbers_list, daily_setup_counts_plot):
    daily_total_inventory = [0] * num_days
    daily_work_times = [0] * num_days
    daily_production_counts = {part: [0] * num_days for part in part_numbers_list}

    current_inventory = {part: 0 for part in part_numbers_list}

    for day_idx, day_schedule in enumerate(best_individual):
        daily_work_time_sec = 0
        produced_today = {part: 0 for part in part_numbers_list}
        
        for part, shots in day_schedule:
            part_data = data_dict[part]
            production_quantity = shots * part_data["cabity"]
            daily_work_time_sec += shots * part_data["cycle_time_per_shot"]
            produced_today[part] += production_quantity
        
        daily_work_times[day_idx] = daily_work_time_sec

        current_daily_total_inventory = 0
        for part in part_numbers_list:
            part_data = data_dict[part]
            demand_today = part_data["demand_per_day"]

            current_inventory[part] += produced_today[part]
            daily_production_counts[part][day_idx] = produced_today[part]

            if current_inventory[part] >= demand_today:
                current_inventory[part] -= demand_today
            else:
                current_inventory[part] = 0

            num_baskets_in_stock = np.ceil(max(0, current_inventory[part]) / part_data["capacity_par_basket"])
            current_daily_total_inventory += current_inventory[part]

        daily_total_inventory[day_idx] = current_daily_total_inventory

    plt.figure(figsize=(12, 6))
    sns.barplot(x=list(range(1, num_days + 1)), y=daily_total_inventory)
    plt.xlabel("期間 (日)")
    plt.ylabel("総在庫数 (個)")
    plt.title("各期間の総在庫数")
    plt.grid(axis='y', linestyle='--')
    plt.tight_layout()
    plt.show()

    plt.figure(figsize=(12, 6))
    sns.barplot(x=list(range(1, num_days + 1)), y=[t / 3600 for t in daily_work_times])
    plt.axhline(y=regular_work_time_sec / 3600, linestyle='--', label='定時稼働時間')
    plt.xlabel("期間 (日)")
    plt.ylabel("稼働時間 (時間)")
    plt.title("各期間の稼働時間")
    plt.legend()
    plt.grid(axis='y', linestyle='--')
    plt.tight_layout()
    plt.show()

    plt.figure(figsize=(14, 7))
    for part in part_numbers_list:
        sns.lineplot(x=list(range(1, num_days + 1)), y=daily_production_counts[part], marker='o', label=part)
    plt.xlabel("期間 (日)")
    plt.ylabel("生産数 (個)")
    plt.title("各期間の各品番生産数")
    plt.legend(title="品番")
    plt.grid(axis='y', linestyle='--')
    plt.tight_layout()
    plt.show()
    
    plt.figure(figsize=(12, 6))
    sns.barplot(x=list(range(1, num_days + 1)), y=daily_setup_counts_plot)
    plt.axhline(y=3, linestyle='--', label=f"型替え上限")
    plt.xlabel("期間 (日)")
    plt.ylabel("型替え回数")
    plt.title("各期間の総型替え回数")
    plt.legend()
    plt.grid(axis='y', linestyle='--')
    plt.tight_layout()
    plt.show()

# --- GA実行をラップする関数 ---
def run_ga_scheduling(
    csv_file_path: str = 'お試しデータ/term1.csv',
    num_days: int = 20,
    regular_work_time_sec: int = 16 * 60 * 60,
    overtime_cost_per_sec: float = 0.5,
    penalty_over_setup_limit: float = 10000000,
    penalty_demand_shortage: float = 100000,
    penalty_deviation_from_average_production: float = 1000,
    population_size: int = 300,
    num_generations: int = 100,
    crossover_prob: float = 0.7,
    mutation_prob: float = 0.3,
    mutate_individual_prob: float = 0.2,
    random_seed: int = 42
):
    try:
        df = pd.read_csv(csv_file_path)
        data_dict = {}
        for index, row in df.iterrows():
            part_number = row['part_number']
            data_dict[part_number] = {
                "inventory_cost_pet_basket": row['inventory_cost_pet_basket'],
                "setup_cost": row['setup_cost'],
                "capacity_par_basket": row['capacity_par_basket'],
                "demand_per_day": row['demand_per_day'],
                "cycle_time_per_shot": row['cycle_time_per_shot(sec)'],
                "setup_time": row['setup_time(sec)'],
                "cabity": row['cabity']
            }
        part_numbers_list = list(data_dict.keys())
    except FileNotFoundError:
        print(f"Error: CSV file not found at {csv_file_path}")
        print("Please make sure the CSV file is in the correct directory.")
        return
    
    random.seed(random_seed)

    # Toolboxの登録解除 (初回呼び出し時は登録されていないためエラーになるのを防ぐ)
    # hasattrで存在を確認してからunregisterする
    if hasattr(toolbox, "attr_day_schedule"): toolbox.unregister("attr_day_schedule")
    if hasattr(toolbox, "individual"): toolbox.unregister("individual")
    if hasattr(toolbox, "population"): toolbox.unregister("population")
    if hasattr(toolbox, "evaluate"): toolbox.unregister("evaluate")
    if hasattr(toolbox, "mutate"): toolbox.unregister("mutate")
    if hasattr(toolbox, "select"): toolbox.unregister("select")
    if hasattr(toolbox, "mate"): toolbox.unregister("mate")

    def generate_day_schedule_for_toolbox():
        day_schedule = []
        max_parts_for_initial_schedule = min(3, len(part_numbers_list))
        parts_to_produce_today = random.sample(part_numbers_list, random.randint(1, max_parts_for_initial_schedule))
        for part in parts_to_produce_today:
            shots = random.randint(10, 100)
            day_schedule.append((part, shots))
        random.shuffle(day_schedule)
        return day_schedule

    toolbox.register("attr_day_schedule", generate_day_schedule_for_toolbox)
    toolbox.register("individual", tools.initRepeat, creator.Individual, toolbox.attr_day_schedule, n=num_days)
    toolbox.register("population", tools.initRepeat, list, toolbox.individual)

    toolbox.register("evaluate", partial(evaluate, 
                                         data_dict=data_dict, 
                                         num_days=num_days, 
                                         regular_work_time_sec=regular_work_time_sec, 
                                         overtime_cost_per_sec=overtime_cost_per_sec, 
                                         penalty_over_setup_limit=penalty_over_setup_limit, 
                                         penalty_demand_shortage=penalty_demand_shortage, 
                                         penalty_deviation_from_average_production=penalty_deviation_from_average_production,
                                         part_numbers_list=part_numbers_list))

    def mutate_schedule_for_toolbox(individual, indpb_mut):
        for i in range(len(individual)):
            if random.random() < indpb_mut:
                max_parts_for_mutation = min(3, len(part_numbers_list)) 
                day_schedule = []
                parts_to_produce_today = random.sample(part_numbers_list, random.randint(1, max_parts_for_mutation))
                for part in parts_to_produce_today:
                    shots = random.randint(10, 100)
                    day_schedule.append((part, shots))
                random.shuffle(day_schedule)
                individual[i] = day_schedule
            else:
                random.shuffle(individual[i])
        return individual,
    
    toolbox.register("mutate", mutate_schedule_for_toolbox, indpb_mut=mutate_individual_prob)
    toolbox.register("select", tools.selTournament, tournsize=3)
    toolbox.register("mate", tools.cxTwoPoint)

    pop = toolbox.population(n=population_size)
    hof = tools.HallOfFame(1)
    stats = tools.Statistics(lambda ind: ind.fitness.values)
    stats.register("avg", np.mean)
    stats.register("std", np.std)
    stats.register("min", np.min)
    stats.register("max", np.max)

    print(f"--- GA実行開始 (世代数: {num_generations}, 個体数: {population_size}) ---")
    pop, log = algorithms.eaSimple(pop, toolbox, cxpb=crossover_prob, mutpb=mutation_prob, ngen=num_generations, 
                                   stats=stats, halloffame=hof, verbose=True)

    print("\n--- 最適なスケジュール ---")
    best_individual = hof[0]
    print(f"最小コスト: {best_individual.fitness.values[0]:.2f}")
    
    for day_idx, day_schedule in enumerate(best_individual):
        print(f"Day {day_idx + 1}:")
        if not day_schedule:
            print("  (No production scheduled)")
        else:
            for part, shots in day_schedule:
                print(f"  - Part: {part}, Shots: {shots}")
    
    total_inventory_cost_final = 0
    total_overtime_cost_final = 0
    total_setup_cost_final = 0
    total_penalty_setup_limit_final = 0
    total_penalty_demand_shortage_final = 0
    total_penalty_leveling_final = 0
    
    current_inventory_final = {part: 0 for part in part_numbers_list}
    daily_production_quantities_final = {part: [0] * num_days for part in part_numbers_list}
    
    daily_setup_counts_for_plot = [0] * num_days

    for day_idx, day_schedule in enumerate(best_individual):
        daily_work_time_sec_final = 0
        produced_today_final = {part: 0 for part in part_numbers_list}
        parts_scheduled_today_final = {part for part, _ in day_schedule}

        for part, shots in day_schedule:
            part_data = data_dict[part]
            production_quantity = shots * part_data["cabity"]
            daily_work_time_sec_final += shots * part_data["cycle_time_per_shot"]
            produced_today_final[part] += production_quantity

        daily_setup_count_final = len(parts_scheduled_today_final)
        daily_setup_counts_for_plot[day_idx] = daily_setup_count_final
        if data_dict:
            total_setup_cost_final += daily_setup_count_final * data_dict[part_numbers_list[0]]["setup_cost"] 

        if daily_work_time_sec_final > regular_work_time_sec:
            overtime_sec = daily_work_time_sec_final - regular_work_time_sec
            total_overtime_cost_final += overtime_sec * overtime_cost_per_sec

        if daily_setup_count_final > 3:
            total_penalty_setup_limit_final += penalty_over_setup_limit * (daily_setup_count_final - 3)

        for part in part_numbers_list:
            part_data = data_dict[part]
            demand_today = part_data["demand_per_day"]
            
            current_inventory_final[part] += produced_today_final[part]
            
            if current_inventory_final[part] >= demand_today:
                current_inventory_final[part] -= demand_today
            else:
                shortage = demand_today - current_inventory_final[part]
                total_penalty_demand_shortage_final += penalty_demand_shortage * shortage
                current_inventory_final[part] = 0

            num_baskets_in_stock = np.ceil(max(0, current_inventory_final[part]) / part_data["capacity_par_basket"])
            total_inventory_cost_final += num_baskets_in_stock * part_data["inventory_cost_pet_basket"]

            daily_production_quantities_final[part][day_idx] = produced_today_final[part]

    for part in part_numbers_list:
        daily_quantities = np.array(daily_production_quantities_final[part])
        if np.sum(daily_quantities) > 0:
            total_penalty_leveling_final += np.std(daily_quantities) * penalty_deviation_from_average_production

    print(f"  合計コスト: {(total_inventory_cost_final + total_overtime_cost_final + total_setup_cost_final + total_penalty_setup_limit_final + total_penalty_demand_shortage_final + total_penalty_leveling_final):.2f}")

    plot_results(best_individual, data_dict, num_days, regular_work_time_sec, part_numbers_list, daily_setup_counts_for_plot)

if __name__ == "__main__":
    print("--- デフォルトパラメータで実行 ---")
    run_ga_scheduling()

    """
    print("\n--- パラメータを変更して実行 (強い段替え制約) ---")
    run_ga_scheduling(
        overtime_cost_per_sec=1.0,
        num_generations=200,
        population_size=500,
        penalty_over_setup_limit=50000000,
        random_seed=123
    )

    print("\n--- さらに別のパラメータで実行 (期間短縮、在庫重視) ---")
    run_ga_scheduling(
        penalty_demand_shortage=5000000,
        penalty_deviation_from_average_production=50,
        num_days=15,
        csv_file_path='お試しデータ/term1.csv',
        penalty_over_setup_limit=10000000
    )
    """

