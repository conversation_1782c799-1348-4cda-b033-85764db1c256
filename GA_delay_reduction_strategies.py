"""
出荷遅れ削減のための改良版GA
複数の戦略を組み合わせて出荷遅れを最小化
"""

import random
import numpy as np
import pandas as pd
import csv
from deap import base, creator, tools, algorithms
import matplotlib.pyplot as plt
import japanize_matplotlib

# グローバル変数
品番リスト = []
出荷数リスト = []
収容数リスト = []
サイクルタイムリスト = []
込め数リスト = []
初期在庫量リスト = []
品番数 = 0
期間 = 20

def read_csv(file_path):
    """CSVファイルを読み込む関数"""
    global 品番リスト, 出荷数リスト, 収容数リスト, サイクルタイムリスト, 込め数リスト, 初期在庫量リスト
    
    with open(file_path, 'r', encoding='utf-8') as file:
        reader = csv.reader(file)
        header = next(reader)
        
        品番リスト = []
        出荷数リスト = []
        収容数リスト = []
        サイクルタイムリスト = []
        込め数リスト = []
        初期在庫量リスト = []
        
        for row in reader:
            if len(row) == 0:
                continue
            品番リスト.append(row[header.index("part_number")])
            出荷数リスト.append(int(row[header.index("shipment")]))
            収容数リスト.append(int(row[header.index("capacity")]))
            サイクルタイムリスト.append(float(row[header.index("cycle_time")])/60)
            込め数リスト.append(int(row[header.index("cabity")]))
            初期在庫量リスト.append(int(row[header.index("initial_inventory")]))
        
        return 品番リスト, 出荷数リスト, 収容数リスト, サイクルタイムリスト, 込め数リスト, 初期在庫量リスト

def calculate_safety_stock():
    """安全在庫を計算"""
    safety_stock = []
    for i in range(品番数):
        # 需要の2-3日分を安全在庫として設定
        safety = max(出荷数リスト[i] * 2, 100)  # 最低100個
        safety_stock.append(safety)
    return safety_stock

def calculate_delay_risk(inventory, demand, cycle_time, period):
    """遅れリスクを計算（優先度決定用）"""
    if inventory <= 0:
        return 1000 + period * 100  # 在庫切れは最高優先度
    
    # 在庫日数を計算
    days_of_inventory = inventory / max(demand, 1)
    
    # リスクスコア（低いほど優先度高）
    risk = (1 / days_of_inventory) * demand * cycle_time
    return risk

def evaluate_with_delay_focus(ind):
    """出荷遅れ重視の評価関数"""
    global 品番数, 期間
    
    total_setup_penalty = 0
    total_overtime_penalty = 0
    total_shortage_penalty = 0
    
    inventory = 初期在庫量リスト[:]
    max_daily_time = (8 + 2) * 60 * 2
    
    for t in range(期間):
        daily_time = 0
        daily_setup = 0
        
        for i in range(品番数):
            idx = t * 品番数 + i
            production = ind[idx]
            
            if inventory[i] >= 出荷数リスト[i]:
                production = 0
            
            if production > 0:
                setup_time = 30
                daily_setup += 1
            else:
                setup_time = 0
            
            if サイクルタイムリスト[i] > 0 and 込め数リスト[i] > 0:
                production_time = production / (サイクルタイムリスト[i] * 込め数リスト[i])
                daily_time += production_time + setup_time
            
            # 在庫更新
            inventory[i] += production - 出荷数リスト[i]
            
            # 強化された出荷遅れペナルティ
            if inventory[i] < 0:
                shortage_amount = abs(inventory[i])
                # 期間が後になるほど重いペナルティ
                period_multiplier = (t + 1) * 2
                # 需要量に比例したペナルティ
                demand_multiplier = 出荷数リスト[i] / 100
                
                delay_penalty = 50000 * shortage_amount * period_multiplier * demand_multiplier
                total_shortage_penalty += delay_penalty
                inventory[i] = 0
        
        # 時間制約ペナルティ
        if daily_time > max_daily_time:
            overtime = daily_time - max_daily_time
            total_overtime_penalty += 1000000 * overtime
        
        # 段替え制約ペナルティ
        if daily_setup > 5:
            total_setup_penalty += 10000 * (daily_setup - 5)
    
    total_penalty = total_setup_penalty + total_overtime_penalty + total_shortage_penalty
    return total_penalty,

def generate_proactive_individual():
    """先読み生産を行う個体生成"""
    productions = []
    temp_inventory = 初期在庫量リスト[:]
    max_daily_time = (8 + 2) * 60 * 2
    safety_stock = calculate_safety_stock()
    
    for t in range(期間):
        daily_time = 0
        daily_productions = [0] * 品番数
        
        # 優先度を計算（遅れリスクの高い順）
        priorities = []
        for i in range(品番数):
            risk = calculate_delay_risk(temp_inventory[i], 出荷数リスト[i], 
                                     サイクルタイムリスト[i], t)
            priorities.append((risk, i))
        
        # リスクの高い順にソート
        priorities.sort(reverse=True)
        
        # 優先度順に生産を検討
        for risk, i in priorities:
            # 安全在庫を下回る場合は積極的に生産
            target_inventory = max(safety_stock[i], 出荷数リスト[i] * 1.5)
            shortage = max(0, target_inventory - temp_inventory[i])
            
            if shortage > 0:
                setup_time = 30
                remaining_time = max_daily_time - daily_time - setup_time
                
                if remaining_time > 0 and サイクルタイムリスト[i] > 0 and 込め数リスト[i] > 0:
                    cycle_time_per_unit = サイクルタイムリスト[i] * 込め数リスト[i]
                    max_producible = int(remaining_time / cycle_time_per_unit)
                    
                    if max_producible > 0:
                        # 不足分を優先的に生産
                        production = min(shortage, max_producible)
                        production_time = setup_time + production * cycle_time_per_unit
                        
                        if daily_time + production_time <= max_daily_time:
                            daily_productions[i] = production
                            daily_time += production_time
        
        # 生産量を記録し、在庫を更新
        for i in range(品番数):
            productions.append(daily_productions[i])
            temp_inventory[i] += daily_productions[i] - 出荷数リスト[i]
            temp_inventory[i] = max(0, temp_inventory[i])
    
    return creator.Individual(productions)

def smart_mutation(ind):
    """遅れ削減を重視した突然変異"""
    max_daily_time = (8 + 2) * 60 * 2
    
    # 現在の解の出荷遅れを評価
    temp_inventory = 初期在庫量リスト[:]
    delay_periods = []
    
    for t in range(期間):
        for i in range(品番数):
            idx = t * 品番数 + i
            production = ind[idx]
            temp_inventory[i] += production - 出荷数リスト[i]
            
            if temp_inventory[i] < 0:
                delay_periods.append((t, i, abs(temp_inventory[i])))
                temp_inventory[i] = 0
    
    # 遅れが発生している期間を優先的に改善
    if delay_periods:
        # 最も遅れの大きい期間を選択
        delay_periods.sort(key=lambda x: x[2], reverse=True)
        target_period, target_product, delay_amount = delay_periods[0]
        
        # その期間より前の期間で生産を増やす
        for t in range(max(0, target_period - 2), target_period):
            idx = t * 品番数 + target_product
            
            # 時間制約内で生産量を増やす
            if random.random() < 0.5:
                increase = random.randint(50, min(200, delay_amount))
                ind[idx] = min(ind[idx] + increase, 1000)
    
    # 通常の突然変異も実行
    for t in range(期間):
        if random.random() < 0.05:  # 5%の確率
            for i in range(品番数):
                if random.random() < 0.3:
                    idx = t * 品番数 + i
                    change = random.randint(-50, 100)  # 増産寄り
                    ind[idx] = max(0, ind[idx] + change)
    
    return ind,

def main_delay_reduction():
    """遅れ削減重視のメイン関数"""
    global 品番数, 期間
    
    # データ読み込み
    result = read_csv('data_ga.csv')
    if result[0] is None:
        print("CSVファイルの読み込みに失敗しました")
        return
    
    品番数 = len(品番リスト)
    期間 = 20
    
    print(f"=== 出荷遅れ削減GA ===")
    print(f"品番数: {品番数}, 期間: {期間}")
    
    # 安全在庫情報を表示
    safety_stock = calculate_safety_stock()
    print(f"安全在庫設定: {safety_stock}")
    
    # DEAP設定
    if hasattr(creator, 'FitnessMin'):
        del creator.FitnessMin
    if hasattr(creator, 'Individual'):
        del creator.Individual
    
    creator.create("FitnessMin", base.Fitness, weights=(-1.0,))
    creator.create("Individual", list, fitness=creator.FitnessMin)
    
    toolbox = base.Toolbox()
    toolbox.register("individual", generate_proactive_individual)
    toolbox.register("population", tools.initRepeat, list, toolbox.individual)
    toolbox.register("evaluate", evaluate_with_delay_focus)
    toolbox.register("mate", tools.cxTwoPoint)
    toolbox.register("mutate", smart_mutation)
    toolbox.register("select", tools.selTournament, tournsize=3)
    
    # GAパラメータ（遅れ削減重視）
    population_size = 150  # 集団サイズを増加
    generations = 100      # 世代数を増加
    cxpb = 0.6
    mutpb = 0.3           # 突然変異率を増加
    
    # 初期集団生成
    population = toolbox.population(n=population_size)
    hof = tools.HallOfFame(1)
    stats = tools.Statistics(lambda ind: ind.fitness.values)
    stats.register("avg", np.mean)
    stats.register("min", np.min)
    
    print("遅れ削減GA開始...")
    
    # GA実行
    population, logbook = algorithms.eaSimple(
        population, toolbox, 
        cxpb=cxpb, mutpb=mutpb, ngen=generations, 
        stats=stats, halloffame=hof, verbose=True
    )
    
    best_ind = hof[0]
    best_fitness = best_ind.fitness.values[0]
    
    print(f"\n=== 遅れ削減GA結果 ===")
    print(f"最良個体のペナルティ: {best_fitness:.2f}")
    
    return best_ind, logbook

if __name__ == "__main__":
    best_solution, log = main_delay_reduction()
