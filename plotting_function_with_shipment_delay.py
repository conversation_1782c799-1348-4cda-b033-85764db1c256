"""
出荷遅れグラフを含む改良版プロット関数
"""

import numpy as np
import matplotlib.pyplot as plt
import japanize_matplotlib

def plot_results_with_shipment_delay(best_individual, 品番数, 期間, 品番リスト, 出荷数リスト, サイクルタイムリスト, 込め数リスト, 初期在庫量リスト):
    """結果をプロットする関数（出荷遅れグラフ付き）"""
    
    # 各期間の結果を計算
    total_inventory_per_period = []
    total_production_time_per_period = []
    total_setup_times_per_period = []
    total_shipment_delay_per_period = []  # 出荷遅れ時間を追加
    
    inventory = 初期在庫量リスト[:]
    
    for t in range(期間):
        daily_inventory = 0
        daily_production_time = 0
        daily_setup_times = 0
        daily_shipment_delay = 0  # 出荷遅れ量を追加
        
        for i in range(品番数):
            idx = t * 品番数 + i
            production = best_individual[idx]
            
            # 在庫が十分にある場合は生産しない
            if inventory[i] >= 出荷数リスト[i]:
                production = 0
            
            # 段替え回数の計算
            if production > 0:
                daily_setup_times += 1
            
            # 生産時間の計算
            if サイクルタイムリスト[i] > 0 and 込め数リスト[i] > 0:
                production_time = production / (サイクルタイムリスト[i] * 込め数リスト[i])
                daily_production_time += production_time
            
            # 在庫更新と出荷遅れ計算
            inventory[i] += production - 出荷数リスト[i]
            
            # 出荷遅れの計算
            if inventory[i] < 0:
                daily_shipment_delay += abs(inventory[i])  # 不足分が出荷遅れ
                inventory[i] = 0  # 負の在庫は0に
            
            daily_inventory += inventory[i]
        
        total_inventory_per_period.append(daily_inventory)
        total_production_time_per_period.append(daily_production_time)
        total_setup_times_per_period.append(daily_setup_times)
        total_shipment_delay_per_period.append(daily_shipment_delay)
    
    # プロット作成（2x2レイアウト）
    fig, axes = plt.subplots(2, 2, figsize=(16, 12))
    fig.suptitle('生産スケジューリング最適化結果（出荷遅れ分析付き）', fontsize=16, fontweight='bold')
    
    periods = list(range(1, 期間 + 1))
    
    # 1. 各期間の総在庫量
    axes[0, 0].bar(periods, total_inventory_per_period, color='skyblue', alpha=0.7)
    axes[0, 0].set_title('各期間の総在庫量', fontweight='bold')
    axes[0, 0].set_xlabel('期間')
    axes[0, 0].set_ylabel('総在庫量 (個)')
    axes[0, 0].grid(True, alpha=0.3)
    
    # 2. 各期間の総生産時間
    axes[0, 1].bar(periods, total_production_time_per_period, color='lightgreen', alpha=0.7)
    axes[0, 1].set_title('各期間の総生産時間', fontweight='bold')
    axes[0, 1].set_xlabel('期間')
    axes[0, 1].set_ylabel('総生産時間 (分)')
    axes[0, 1].grid(True, alpha=0.3)
    
    # 3. 各期間の総段替え回数
    axes[1, 0].bar(periods, total_setup_times_per_period, color='salmon', alpha=0.7)
    axes[1, 0].set_title('各期間の総段替え回数', fontweight='bold')
    axes[1, 0].set_xlabel('期間')
    axes[1, 0].set_ylabel('段替え回数')
    axes[1, 0].grid(True, alpha=0.3)
    
    # 段替え上限ラインを追加
    axes[1, 0].axhline(y=5, color='red', linestyle='--', alpha=0.8, label='上限 (5回)')
    axes[1, 0].legend()
    
    # 4. 各期間の総出荷遅れ量
    bars = axes[1, 1].bar(periods, total_shipment_delay_per_period, color='orange', alpha=0.7)
    axes[1, 1].set_title('各期間の総出荷遅れ量', fontweight='bold')
    axes[1, 1].set_xlabel('期間')
    axes[1, 1].set_ylabel('出荷遅れ量 (個)')
    axes[1, 1].grid(True, alpha=0.3)
    
    # 出荷遅れがある期間を赤色で強調表示
    for i, (period, delay) in enumerate(zip(periods, total_shipment_delay_per_period)):
        if delay > 0:
            bars[i-1].set_color('red')
            bars[i-1].set_alpha(0.8)
    
    # 出荷遅れゼロのラインを追加
    axes[1, 1].axhline(y=0, color='green', linestyle='-', alpha=0.8, label='遅れなし')
    axes[1, 1].legend()
    
    plt.tight_layout()
    plt.show()
    
    # 統計情報を出力
    print("\n=== 出荷遅れ分析 ===")
    total_delay = sum(total_shipment_delay_per_period)
    delay_periods = sum(1 for x in total_shipment_delay_per_period if x > 0)
    max_delay = max(total_shipment_delay_per_period)
    
    print(f"総出荷遅れ量: {total_delay:.1f} 個")
    print(f"出荷遅れ発生期間: {delay_periods} / {期間} 期間")
    print(f"最大出荷遅れ量: {max_delay:.1f} 個")
    print(f"出荷遅れ率: {(delay_periods / 期間 * 100):.1f}%")
    
    if delay_periods > 0:
        print(f"平均出荷遅れ量（遅れ発生期間のみ）: {(total_delay / delay_periods):.1f} 個")
        delay_periods_list = [i+1 for i, x in enumerate(total_shipment_delay_per_period) if x > 0]
        print(f"出荷遅れ発生期間: {delay_periods_list}")
    else:
        print("出荷遅れは発生していません！")
    
    return total_inventory_per_period, total_production_time_per_period, total_setup_times_per_period, total_shipment_delay_per_period

# 使用例（既存のコードに追加する場合）
def integrate_with_existing_code():
    """
    既存のコードに統合する方法の例
    """
    print("""
    既存のplot_results関数を以下のように置き換えてください：
    
    # 古い関数呼び出し
    # inventory_data, production_time_data, setup_data = plot_results(best_ind)
    
    # 新しい関数呼び出し
    inventory_data, production_time_data, setup_data, shipment_delay_data = plot_results_with_shipment_delay(
        best_ind, 品番数, 期間, 品番リスト, 出荷数リスト, サイクルタイムリスト, 込め数リスト, 初期在庫量リスト
    )
    """)

if __name__ == "__main__":
    integrate_with_existing_code()
